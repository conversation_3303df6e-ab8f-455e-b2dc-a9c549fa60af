import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/presentation/onboarding/screens/language_selection_screen.dart';
import 'package:agent_kujukiza/presentation/onboarding/screens/sms_permission_screen.dart';
import 'package:agent_kujukiza/presentation/onboarding/screens/battery_optimization_screen.dart';
import 'package:agent_kujukiza/presentation/onboarding/screens/setup_complete_screen.dart';
import 'package:agent_kujukiza/presentation/dashboard/dashboard_screen.dart';
import 'package:agent_kujukiza/presentation/transactions/transactions_screen.dart';
import 'package:agent_kujukiza/presentation/export/export_screen.dart';
import 'package:agent_kujukiza/presentation/settings/settings_screen.dart';
import 'package:agent_kujukiza/presentation/subscription/lock_screen.dart';
import 'package:agent_kujukiza/core/storage/local_prefs.dart';
import 'package:agent_kujukiza/data/services/subscription_service.dart';

// Route Names
class AppRoutes {
  static const String onboardingLanguage = '/onboarding/language';
  static const String onboardingSmsPermission = '/onboarding/sms-permission';
  static const String onboardingBatteryOptimization = '/onboarding/battery-optimization';
  static const String onboardingSetupComplete = '/onboarding/setup-complete';
  static const String dashboard = '/dashboard';
  static const String transactions = '/transactions';
  static const String export = '/export';
  static const String settings = '/settings';
  static const String subscriptionLock = '/subscription-lock';
  static const String root = '/';
}

class AppRouter {
  static final GoRouter router = GoRouter(
    initialLocation: AppRoutes.onboardingLanguage,
    redirect: (context, state) async {
      // Check if onboarding is complete
      final isOnboardingComplete = await LocalPrefs.isOnboardingComplete();

      // If onboarding is not complete, redirect to onboarding
      if (!isOnboardingComplete && !(state.fullPath?.startsWith('/onboarding') ?? false)) {
        return AppRoutes.onboardingLanguage;
      }

      // If onboarding is complete, check subscription status
      if (isOnboardingComplete) {
        final canUseApp = await SubscriptionService.canUseApp();

        // If subscription is expired and not on lock screen, redirect to lock screen
        if (!canUseApp && state.fullPath != AppRoutes.subscriptionLock) {
          return AppRoutes.subscriptionLock;
        }

        // If subscription is active and on lock screen, redirect to dashboard
        if (canUseApp && state.fullPath == AppRoutes.subscriptionLock) {
          return AppRoutes.dashboard;
        }

        // If trying to access onboarding when already complete, redirect to dashboard
        if (state.fullPath?.startsWith('/onboarding') ?? false) {
          return canUseApp ? AppRoutes.dashboard : AppRoutes.subscriptionLock;
        }
      }

      // No redirect needed
      return null;
    },
    routes: [
      // Onboarding Flow
      GoRoute(
        path: AppRoutes.onboardingLanguage,
        name: 'onboarding-language',
        builder: (context, state) => const LanguageSelectionScreen(),
      ),
      GoRoute(
        path: AppRoutes.onboardingSmsPermission,
        name: 'onboarding-sms-permission',
        builder: (context, state) => const SmsPermissionScreen(),
      ),
      GoRoute(
        path: AppRoutes.onboardingBatteryOptimization,
        name: 'onboarding-battery-optimization',
        builder: (context, state) => const BatteryOptimizationScreen(),
      ),
      GoRoute(
        path: AppRoutes.onboardingSetupComplete,
        name: 'onboarding-setup-complete',
        builder: (context, state) => const SetupCompleteScreen(),
      ),

      // Main App Flow
      GoRoute(
        path: AppRoutes.dashboard,
        name: 'dashboard',
        builder: (context, state) => const DashboardScreen(),
      ),
      GoRoute(
        path: AppRoutes.transactions,
        name: 'transactions',
        builder: (context, state) => const TransactionsScreen(),
      ),
      GoRoute(
        path: AppRoutes.export,
        name: 'export',
        builder: (context, state) => const ExportScreen(),
      ),
      GoRoute(
        path: AppRoutes.settings,
        name: 'settings',
        builder: (context, state) => const SettingsScreen(),
      ),

      // Subscription Lock Screen
      GoRoute(
        path: AppRoutes.subscriptionLock,
        name: 'subscription-lock',
        builder: (context, state) => const SubscriptionLockScreen(),
      ),

      // Redirect root to onboarding
      GoRoute(
        path: AppRoutes.root,
        redirect: (_, _) => AppRoutes.onboardingLanguage,
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      body: Center(
        child: Text('Page not found: ${state.uri}'),
      ),
    ),
  );
}
