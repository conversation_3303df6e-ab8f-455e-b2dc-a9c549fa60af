import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocaleProvider with ChangeNotifier {
  static const String _localeKey = 'locale';
  
  Locale _locale = const Locale('en');
  
  Locale get locale => _locale;
  
  LocaleProvider() {
    _loadLocale();
  }
  
  Future<void> _loadLocale() async {
    final prefs = await SharedPreferences.getInstance();
    final localeCode = prefs.getString(_localeKey);
    
    if (localeCode != null) {
      _locale = Locale(localeCode);
      notifyListeners();
    }
  }
  
  Future<void> setLocale(Locale locale) async {
    if (!['en', 'lg'].contains(locale.languageCode)) {
      return;
    }
    
    _locale = locale;
    
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_localeKey, locale.languageCode);
    
    notifyListeners();
  }
  
  static const List<Locale> supportedLocales = [
    Locale('en'),
    Locale('lg'),
  ];
}
