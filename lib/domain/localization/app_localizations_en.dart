// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Agent Kuju<PERSON>za';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get selectLanguageSubtitle =>
      'Choose your preferred language to continue';

  @override
  String get english => 'English';

  @override
  String get luganda => 'Luganda';

  @override
  String get next => 'Next';

  @override
  String get skip => 'Skip';

  @override
  String get getStarted => 'Get Started';

  @override
  String get back => 'Back';

  @override
  String get smsPermissionTitle => 'SMS Access Required';

  @override
  String get smsPermissionMessage =>
      'Agent <PERSON>ju<PERSON> needs access to your SMS to track mobile money transactions automatically.';

  @override
  String get allowSmsAccess => 'Allow SMS Access';

  @override
  String get smsAccessExplanation =>
      'We\'ll only read messages from mobile money numbers to track your transactions.';

  @override
  String get batteryOptimizationTitle => 'Battery Optimization';

  @override
  String get batteryOptimizationMessage =>
      'To ensure reliable operation, please disable battery optimization for Agent Kujukiza.';

  @override
  String get enableBatteryOptimization => 'Enable';

  @override
  String get batteryOptimizationExplanation =>
      'This allows the app to run in the background and process transactions.';

  @override
  String get setupCompleteTitle => 'Setup Complete!';

  @override
  String get setupCompleteMessage =>
      'You\'re all set to start tracking your mobile money transactions.';

  @override
  String get goToDashboard => 'Go to Dashboard';

  @override
  String get continueText => 'Continue';

  @override
  String get cancel => 'Cancel';

  @override
  String get ok => 'OK';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get loading => 'Loading...';

  @override
  String get currentFloat => 'Current Float';

  @override
  String get floatOk => 'OK';

  @override
  String get lowFloat => 'Low Float!';

  @override
  String get deposits => 'Deposits';

  @override
  String get withdrawals => 'Withdrawals';

  @override
  String get floatTopUp => 'Float Top-Up';

  @override
  String get netChange => 'Net';

  @override
  String get commission => 'Commission';

  @override
  String get recentTransactions => 'Recent Transactions';

  @override
  String get viewAllTransactions => 'View All Transactions';

  @override
  String get home => 'Home';

  @override
  String get transactions => 'T-List';

  @override
  String get export => 'Export';

  @override
  String get settings => 'Settings';

  @override
  String get dashboard => 'Dashboard';

  @override
  String get noTransactionsYet => 'No transactions yet';

  @override
  String get refresh => 'Refresh';

  @override
  String get today => 'Today';

  @override
  String get thisWeek => 'This Week';

  @override
  String get thisMonth => 'This Month';

  @override
  String get custom => 'Custom';

  @override
  String get floatAsOf => 'Float as of';

  @override
  String get floatStatus => 'Float Status';

  @override
  String get sufficient => 'Sufficient';

  @override
  String get lowBalance => 'Low Balance';

  @override
  String get lastUpdated => 'Last updated';

  @override
  String get selectPeriod => 'Select Period';

  @override
  String get startingFloat => 'Starting Float';

  @override
  String get dailyRevenue => 'Daily Revenue';

  @override
  String get extraAmount => 'Extra';

  @override
  String get checkBalance => 'Check Balance';

  @override
  String get missing => 'missing';

  @override
  String get selectDate => 'Select Date';

  @override
  String get dailyAccountingSummary => 'Daily Accounting Summary';

  @override
  String get startingFloatToday => 'Starting Float for Today';

  @override
  String get startingFloatDescription => 'Money you began the day with';

  @override
  String get moneyEarnedSoFar => 'Money Earned So Far';

  @override
  String get dailyRevenueDescription => 'Commission from transactions today';

  @override
  String get floatIncreaseToday => 'Float Increase for Today';

  @override
  String get floatIncreaseDescription => 'How much your float has grown';

  @override
  String get accountingFormula => 'Starting + Earned = Expected Float';

  @override
  String get switchLanguage => 'Switch Language';

  @override
  String get languageSwitched => 'Language switched successfully';

  @override
  String get search => 'Search';

  @override
  String get filter => 'Filter';

  @override
  String get searchByNameOrAmount => 'Search by name or amount';

  @override
  String get noMatchesFound => 'No matches found';

  @override
  String get clearAll => 'Clear All';

  @override
  String get subscription => 'Subscription';

  @override
  String get subscriptionActive => 'Active';

  @override
  String get subscriptionExpired => 'Expired';

  @override
  String get renewNow => 'Renew Now';

  @override
  String get yongera => 'Yongera';

  @override
  String get daysRemaining => 'days remaining';

  @override
  String get subscriptionExpiredMessage => 'Your subscription has expired';

  @override
  String get renewSubscriptionMessage =>
      'To continue using all features, renew your monthly subscription';

  @override
  String get monthlyFee => 'Monthly Fee: UGX 3,000';

  @override
  String get appInformation => 'App Information';

  @override
  String get version => 'Version';

  @override
  String get platform => 'Platform';

  @override
  String get dataStorage => 'Data Storage';

  @override
  String get localDevice => 'Local Device';

  @override
  String get howToUse => 'How to use Agent Kujukiza';

  @override
  String get enkola => 'Enkola y\'okukozesa';

  @override
  String get step1 => 'Allow SMS';

  @override
  String get step2 => 'Get Float';

  @override
  String get step3 => 'View Transactions';

  @override
  String get step1Description =>
      'Grant SMS permissions to read transaction messages';

  @override
  String get step2Description => 'Top up your mobile money float';

  @override
  String get step3Description => 'Monitor your transactions and earnings';

  @override
  String get paymentMethods => 'Payment Methods';

  @override
  String get mtnMobileMoney => 'MTN Mobile Money';

  @override
  String get airtelMoney => 'Airtel Money';

  @override
  String get cashVoucher => 'Cash Voucher';

  @override
  String get ussdPayment => 'USSD Payment';

  @override
  String get selectPaymentMethod => 'Select Payment Method';

  @override
  String get processingPayment => 'Processing Payment...';

  @override
  String get paymentSuccessful => 'Payment Successful!';

  @override
  String get paymentFailed => 'Payment Failed';

  @override
  String get subscriptionRenewed => 'Your subscription has been renewed until';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get debugSection => 'Debug Section';

  @override
  String get expireSubscription => 'Expire Subscription';

  @override
  String get renewSubscription => 'Renew Subscription';

  @override
  String get done => 'Done';

  @override
  String get setupYourFloat => 'Setup Your Float';

  @override
  String get enterCurrentBalancePrompt =>
      'Enter your current balance on MTN and Airtel mobile money agent accounts to begin tracking your daily float.';

  @override
  String get mtnFloat => 'MTN Float (UGX)';

  @override
  String get airtelFloat => 'Airtel Float (UGX)';

  @override
  String get floatSetupInfo =>
      'This information is stored securely on your device and helps track your daily earnings and float changes.';

  @override
  String get continueToApp => 'Continue to App';

  @override
  String get pleaseEnterAmount => 'Please enter an amount';

  @override
  String get pleaseEnterValidAmount => 'Please enter a valid amount';

  @override
  String get floatSetupError => 'Failed to setup float. Please try again.';

  @override
  String get mtn => 'MTN';

  @override
  String get airtel => 'Airtel';

  @override
  String get xtraFloatActive => 'XtraFloat Active';

  @override
  String get xtraFloatInactive => 'XtraFloat Inactive';

  @override
  String get amountHint => '0';

  @override
  String get ugxPrefix => 'UGX ';

  @override
  String get fromStartingFloat => 'from starting float';

  @override
  String get justNow => 'just now';

  @override
  String get minutesAgo => 'm ago';

  @override
  String get hoursAgo => 'h ago';

  @override
  String get daysAgo => 'd ago';

  @override
  String get dailyMetrics => 'Daily Metrics';

  @override
  String get commissionLabel => 'Commission:';

  @override
  String get balanceLabel => 'Bal:';

  @override
  String get balanceUnknown => 'Bal: –––';

  @override
  String get todayLabel => 'Today';

  @override
  String get yesterdayLabel => 'Yesterday';

  @override
  String get transactionLabel => 'Transaction';

  @override
  String get fullSmsLabel => '📄 Full SMS:';

  @override
  String get transactionIdLabel => '↳ Transaction ID:';

  @override
  String get commissionDetailsLabel => '💰 Commission:';

  @override
  String get close => 'Close';
}
