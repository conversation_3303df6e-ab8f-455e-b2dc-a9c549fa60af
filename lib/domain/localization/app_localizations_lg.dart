// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Ganda Luganda (`lg`).
class AppLocalizationsLg extends AppLocalizations {
  AppLocalizationsLg([String locale = 'lg']) : super(locale);

  @override
  String get appTitle => 'Agent Kuju<PERSON>';

  @override
  String get selectLanguage => 'Londa Olulimi';

  @override
  String get selectLanguageSubtitle =>
      'Londa olulimi lw\'oyagala okugenda mu maaso';

  @override
  String get english => 'Luzungu';

  @override
  String get luganda => 'Luganda';

  @override
  String get next => 'Ddala';

  @override
  String get skip => 'Vairamu';

  @override
  String get getStarted => 'Tandika';

  @override
  String get back => 'Emabega';

  @override
  String get smsPermissionTitle => 'Okukkirizibwa Okusoma SMS';

  @override
  String get smsPermissionMessage =>
      'Agent <PERSON><PERSON><PERSON><PERSON> yetaaga okukkirizibwa okusoma SMS zo okusobola okulonda enkola ya sente z\'ekikono.';

  @override
  String get allowSmsAccess => 'Kkiriza Okusoma SMS';

  @override
  String get smsAccessExplanation =>
      'Tujja kusoma message zokka okuva mu namba za mobile money okulaba enkola y\'okukwata sente.';

  @override
  String get batteryOptimizationTitle => 'Okukozesa Bateri Nga Bw\'okiriisa';

  @override
  String get batteryOptimizationMessage =>
      'Okusobola okukola bulungi, sabiwo omukono ku battery optimization ya Agent Kujukiza.';

  @override
  String get enableBatteryOptimization => 'Kkiriza';

  @override
  String get batteryOptimizationExplanation =>
      'Kino kikukkiriza okukola mu nzirugavu n\'okukwata sente zonna.';

  @override
  String get setupCompleteTitle => 'Omulimu Ogwedde!';

  @override
  String get setupCompleteMessage =>
      'Wetegefu kutandika okulaba enkola y\'okukwata sente zo.';

  @override
  String get goToDashboard => 'Genda Ku Dashboard';

  @override
  String get continueText => 'Genda Mberi';

  @override
  String get cancel => 'Sazaamu';

  @override
  String get ok => 'Kale';

  @override
  String get error => 'Ekizibu';

  @override
  String get success => 'Kikolebwa';

  @override
  String get loading => 'Bikola...';

  @override
  String get currentFloat => 'Ggulawo Ekikadde';

  @override
  String get floatOk => 'Ki?';

  @override
  String get lowFloat => 'Ekikadde ekyatono!';

  @override
  String get deposits => 'Ebintu ebyo';

  @override
  String get withdrawals => 'Okukola enkola';

  @override
  String get floatTopUp => 'Gula Float';

  @override
  String get netChange => 'Ekikadde ky\'okusobola';

  @override
  String get commission => 'Amagoba';

  @override
  String get recentTransactions => 'Obulamu bw\'Essimu obw\'akamaanyi';

  @override
  String get viewAllTransactions => 'Laba Byonna';

  @override
  String get home => 'Ekifo';

  @override
  String get transactions => 'Obulamu';

  @override
  String get export => 'Ssendawo';

  @override
  String get settings => 'Enjizze';

  @override
  String get dashboard => 'Ekifo';

  @override
  String get noTransactionsYet => 'Waliwo tekintu kyonna';

  @override
  String get refresh => 'Sikiza';

  @override
  String get today => 'Leero';

  @override
  String get thisWeek => 'Wiiki eno';

  @override
  String get thisMonth => 'Omwezi guno';

  @override
  String get custom => 'Londa';

  @override
  String get floatAsOf => 'Float nga';

  @override
  String get floatStatus => 'Embeera ya Float';

  @override
  String get sufficient => 'Emala';

  @override
  String get lowBalance => 'Ekyatono';

  @override
  String get lastUpdated => 'Kyakyusibwa';

  @override
  String get selectPeriod => 'Londa Ekiseera';

  @override
  String get startingFloat => 'Float ey\'okutandikira';

  @override
  String get dailyRevenue => 'Amagoba g\'olunaku';

  @override
  String get extraAmount => 'Ekyongezebwa';

  @override
  String get checkBalance => 'Kebera Balanse';

  @override
  String get missing => 'ekyabulako';

  @override
  String get selectDate => 'Londa Olunaku';

  @override
  String get dailyAccountingSummary => 'Okubala kw\'Olunaku';

  @override
  String get startingFloatToday => 'Float ey\'Okutandikira Leero';

  @override
  String get startingFloatDescription => 'Ssente ze watandikira olunaku nazo';

  @override
  String get moneyEarnedSoFar => 'Ssente z\'Ofunye Okutuusa Kati';

  @override
  String get dailyRevenueDescription => 'Amagoba okuva mu nkolagana za leero';

  @override
  String get floatIncreaseToday => 'Okweyongera kwa Float Leero';

  @override
  String get floatIncreaseDescription => 'Float yo bw\'eyongedde';

  @override
  String get accountingFormula => 'Okutandikira + Okufuna = Float Esuubirwa';

  @override
  String get switchLanguage => 'Kyuusa Olulimi';

  @override
  String get languageSwitched => 'Olulimi lukyusiddwa obulungi';

  @override
  String get search => 'Laba';

  @override
  String get filter => 'Sengejja';

  @override
  String get searchByNameOrAmount => 'Laba ku dinigwo oba obungi';

  @override
  String get noMatchesFound => 'Tekinatala kulabika';

  @override
  String get clearAll => 'Gyawo Byonna';

  @override
  String get subscription => 'Okusasula';

  @override
  String get subscriptionActive => 'Kikola';

  @override
  String get subscriptionExpired => 'Kiggwaawo';

  @override
  String get renewNow => 'Yongera Kati';

  @override
  String get yongera => 'Yongera';

  @override
  String get daysRemaining => 'ennaku ezisigadde';

  @override
  String get subscriptionExpiredMessage => 'Okusasula kwo kuggwaawo';

  @override
  String get renewSubscriptionMessage =>
      'Okugenda mu maaso n\'okukozesa ebintu byonna, yongera okusasula kwa buli mwezi';

  @override
  String get monthlyFee => 'Okusasula kwa Mwezi: UGX 3,000';

  @override
  String get appInformation => 'Ebikwata ku App';

  @override
  String get version => 'Omutindo';

  @override
  String get platform => 'Platform';

  @override
  String get dataStorage => 'Okutereka Data';

  @override
  String get localDevice => 'Ku Simu';

  @override
  String get howToUse => 'Enkola y\'okukozesa Agent Kujukiza';

  @override
  String get enkola => 'Enkola y\'okukozesa';

  @override
  String get step1 => 'Kkiriza SMS';

  @override
  String get step2 => 'Funa Float';

  @override
  String get step3 => 'Laba Transactions';

  @override
  String get step1Description =>
      'Wa olukusa lw\'okusoma obubaka bwa transactions';

  @override
  String get step2Description => 'Yongera ssente mu mobile money float yo';

  @override
  String get step3Description => 'Londoola transactions zo n\'amagoba go';

  @override
  String get paymentMethods => 'Engeri z\'okusasula';

  @override
  String get mtnMobileMoney => 'MTN Mobile Money';

  @override
  String get airtelMoney => 'Airtel Money';

  @override
  String get cashVoucher => 'Cash Voucher';

  @override
  String get ussdPayment => 'USSD Payment';

  @override
  String get selectPaymentMethod => 'Londa Engeri y\'okusasula';

  @override
  String get processingPayment => 'Tukola ku kusasula...';

  @override
  String get paymentSuccessful => 'Osasudde obulungi!';

  @override
  String get paymentFailed => 'Okusasula tekukoze';

  @override
  String get subscriptionRenewed => 'Okusasula kwo kyongereddwa okutuusa ku';

  @override
  String get tryAgain => 'Ddamu Gezaako';

  @override
  String get debugSection => 'Debug Section';

  @override
  String get expireSubscription => 'Komya Subscription';

  @override
  String get renewSubscription => 'Yongera Subscription';

  @override
  String get done => 'Kiwedde';

  @override
  String get setupYourFloat => 'Tegeka Float Yo';

  @override
  String get enterCurrentBalancePrompt =>
      'Yingiza ssente zo eziri ku MTN ne Airtel mobile money agent accounts okutandika okukuuma float yo ya buli lunaku.';

  @override
  String get mtnFloat => 'MTN Float (UGX)';

  @override
  String get airtelFloat => 'Airtel Float (UGX)';

  @override
  String get floatSetupInfo =>
      'Obubaka buno bukuumibwa mu simu yo era buyamba okukuuma ssente zo n\'enkyukakyuka ya buli lunaku.';

  @override
  String get continueToApp => 'Genda mu App';

  @override
  String get pleaseEnterAmount => 'Yingiza ssente';

  @override
  String get pleaseEnterValidAmount => 'Yingiza ssente ezituufu';

  @override
  String get floatSetupError => 'Okutegeka float tekukoze. Ddamu gezaako.';

  @override
  String get mtn => 'MTN';

  @override
  String get airtel => 'Airtel';

  @override
  String get xtraFloatActive => 'XtraFloat Ekola';

  @override
  String get xtraFloatInactive => 'XtraFloat Tekola';

  @override
  String get amountHint => '0';

  @override
  String get ugxPrefix => 'UGX ';

  @override
  String get fromStartingFloat => 'okuva ku float ya kutandika';

  @override
  String get justNow => 'kati kati';

  @override
  String get minutesAgo => 'dakiika emabega';

  @override
  String get hoursAgo => 'saawa emabega';

  @override
  String get daysAgo => 'nnaku emabega';

  @override
  String get dailyMetrics => 'Ebipimo bya Buli Lunaku';

  @override
  String get commissionLabel => 'Komisoni:';

  @override
  String get balanceLabel => 'Ssente:';

  @override
  String get balanceUnknown => 'Ssente: –––';

  @override
  String get todayLabel => 'Leero';

  @override
  String get yesterdayLabel => 'Jjo';

  @override
  String get transactionLabel => 'Mulimu';

  @override
  String get fullSmsLabel => '📄 SMS Yonna:';

  @override
  String get transactionIdLabel => '↳ Mulimu ID:';

  @override
  String get commissionDetailsLabel => '💰 Komisoni:';

  @override
  String get close => 'Ggalawo';
}
