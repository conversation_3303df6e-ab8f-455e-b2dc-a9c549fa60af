import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class AppLocalizations {
  final Locale locale;
  Map<String, String> _localizedStrings = {};

  AppLocalizations(this.locale);

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  Future<bool> load() async {
    String jsonContent = await rootBundle
        .loadString('l10n/app_${locale.languageCode}.arb');
    final Map<String, dynamic> jsonMap =
        const JsonDecoder().convert(jsonContent) as Map<String, dynamic>;

    // Filter out metadata entries (keys starting with @)
    _localizedStrings = Map<String, String>.fromEntries(
      jsonMap.entries
          .where((entry) => !entry.key.startsWith('@'))
          .map((entry) => MapEntry(entry.key, entry.value.toString())),
    );
    return true;
  }

  String translate(String key) => _localizedStrings[key] ?? key;

  // Getters for localized strings
  String get appTitle => translate('appTitle');
  String get selectLanguage => translate('selectLanguage');
  String get selectLanguageSubtitle => translate('selectLanguageSubtitle');
  String get english => translate('english');
  String get luganda => translate('luganda');
  String get next => translate('next');
  String get skip => translate('skip');
  String get getStarted => translate('getStarted');
  String get back => translate('back');
  String get smsPermissionTitle => translate('smsPermissionTitle');
  String get smsPermissionMessage => translate('smsPermissionMessage');
  String get allowSmsAccess => translate('allowSmsAccess');
  String get smsAccessExplanation => translate('smsAccessExplanation');
  String get batteryOptimizationTitle => translate('batteryOptimizationTitle');
  String get batteryOptimizationMessage =>
      translate('batteryOptimizationMessage');
  String get enableBatteryOptimization =>
      translate('enableBatteryOptimization');
  String get batteryOptimizationExplanation =>
      translate('batteryOptimizationExplanation');
  String get setupCompleteTitle => translate('setupCompleteTitle');
  String get setupCompleteMessage => translate('setupCompleteMessage');
  String get goToDashboard => translate('goToDashboard');
  String get continueText => translate('continue');
  String get cancel => translate('cancel');
  String get ok => translate('ok');
  String get error => translate('error');
  String get success => translate('success');
  String get loading => translate('loading');

  // Dashboard
  String get currentFloat => translate('currentFloat');
  String get floatOk => translate('floatOk');
  String get lowFloat => translate('lowFloat');
  String get deposits => translate('deposits');
  String get withdrawals => translate('withdrawals');
  String get floatTopUp => translate('floatTopUp');
  String get netChange => translate('netChange');
  String get commission => translate('commission');
  String get recentTransactions => translate('recentTransactions');
  String get viewAllTransactions => translate('viewAllTransactions');
  String get home => translate('home');
  String get transactions => translate('transactions');
  String get export => translate('export');
  String get settings => translate('settings');
  String get dashboard => translate('dashboard');
  String get noTransactionsYet => translate('noTransactionsYet');
  String get refresh => translate('refresh');
  String get today => translate('today');
  String get thisWeek => translate('thisWeek');
  String get thisMonth => translate('thisMonth');
  String get custom => translate('custom');
  String get floatAsOf => translate('floatAsOf');
  String get floatStatus => translate('floatStatus');
  String get sufficient => translate('sufficient');
  String get lowBalance => translate('lowBalance');
  String get lastUpdated => translate('lastUpdated');
  String get selectPeriod => translate('selectPeriod');
  String get startingFloat => translate('startingFloat');
  String get dailyRevenue => translate('dailyRevenue');
  String get extraAmount => translate('extraAmount');
  String get checkBalance => translate('checkBalance');
  String get missing => translate('missing');
  String get selectDate => translate('selectDate');
  String get dailyAccountingSummary => translate('dailyAccountingSummary');
  String get startingFloatToday => translate('startingFloatToday');
  String get startingFloatDescription => translate('startingFloatDescription');
  String get moneyEarnedSoFar => translate('moneyEarnedSoFar');
  String get dailyRevenueDescription => translate('dailyRevenueDescription');
  String get floatIncreaseToday => translate('floatIncreaseToday');
  String get floatIncreaseDescription => translate('floatIncreaseDescription');
  String get accountingFormula => translate('accountingFormula');
  String get switchLanguage => translate('switchLanguage');
  String get languageSwitched => translate('languageSwitched');
  String get search => translate('search');
  String get filter => translate('filter');
  String get searchByNameOrAmount => translate('searchByNameOrAmount');
  String get noMatchesFound => translate('noMatchesFound');
  String get clearAll => translate('clearAll');
  String get subscription => translate('subscription');
  String get subscriptionActive => translate('subscriptionActive');
  String get subscriptionExpired => translate('subscriptionExpired');
  String get renewNow => translate('renewNow');
  String get yongera => translate('yongera');
  String get daysRemaining => translate('daysRemaining');
  String get subscriptionExpiredMessage => translate('subscriptionExpiredMessage');
  String get renewSubscriptionMessage => translate('renewSubscriptionMessage');
  String get monthlyFee => translate('monthlyFee');
  String get appInformation => translate('appInformation');
  String get version => translate('version');
  String get platform => translate('platform');
  String get dataStorage => translate('dataStorage');
  String get localDevice => translate('localDevice');
  String get howToUse => translate('howToUse');
  String get enkola => translate('enkola');
  String get step1 => translate('step1');
  String get step2 => translate('step2');
  String get step3 => translate('step3');
  String get step1Description => translate('step1Description');
  String get step2Description => translate('step2Description');
  String get step3Description => translate('step3Description');
  String get paymentMethods => translate('paymentMethods');
  String get mtnMobileMoney => translate('mtnMobileMoney');
  String get airtelMoney => translate('airtelMoney');
  String get cashVoucher => translate('cashVoucher');
  String get ussdPayment => translate('ussdPayment');
  String get selectPaymentMethod => translate('selectPaymentMethod');
  String get processingPayment => translate('processingPayment');
  String get paymentSuccessful => translate('paymentSuccessful');
  String get paymentFailed => translate('paymentFailed');
  String get subscriptionRenewed => translate('subscriptionRenewed');
  String get tryAgain => translate('tryAgain');
  String get debugSection => translate('debugSection');
  String get expireSubscription => translate('expireSubscription');
  String get renewSubscription => translate('renewSubscription');
  String get done => translate('done');

  // Float Setup
  String get setupYourFloat => translate('setupYourFloat');
  String get enterCurrentBalancePrompt => translate('enterCurrentBalancePrompt');
  String get mtnFloat => translate('mtnFloat');
  String get airtelFloat => translate('airtelFloat');
  String get floatSetupInfo => translate('floatSetupInfo');
  String get continueToApp => translate('continueToApp');
  String get pleaseEnterAmount => translate('pleaseEnterAmount');
  String get pleaseEnterValidAmount => translate('pleaseEnterValidAmount');
  String get floatSetupError => translate('floatSetupError');
  String get mtn => translate('mtn');
  String get airtel => translate('airtel');
  String get xtraFloatActive => translate('xtraFloatActive');
  String get xtraFloatInactive => translate('xtraFloatInactive');
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  bool isSupported(Locale locale) {
    return ['en', 'lg'].contains(locale.languageCode);
  }

  @override
  Future<AppLocalizations> load(Locale locale) async {
    AppLocalizations localizations = AppLocalizations(locale);
    await localizations.load();
    return localizations;
  }

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

// Extension to simplify calling translations
// Usage: context.l10n.appTitle
// Instead of: AppLocalizations.of(context)!.appTitle
extension LocalizationExtension on BuildContext {
  AppLocalizations get l10n => AppLocalizations.of(this)!;
}
