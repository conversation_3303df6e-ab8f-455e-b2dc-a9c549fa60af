import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_lg.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'localization/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('lg'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'Agent Kujukiza'**
  String get appTitle;

  /// Title for language selection screen
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// No description provided for @selectLanguageSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Choose your preferred language to continue'**
  String get selectLanguageSubtitle;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @luganda.
  ///
  /// In en, this message translates to:
  /// **'Luganda'**
  String get luganda;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @getStarted.
  ///
  /// In en, this message translates to:
  /// **'Get Started'**
  String get getStarted;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @smsPermissionTitle.
  ///
  /// In en, this message translates to:
  /// **'SMS Access Required'**
  String get smsPermissionTitle;

  /// No description provided for @smsPermissionMessage.
  ///
  /// In en, this message translates to:
  /// **'Agent Kujukiza needs access to your SMS to track mobile money transactions automatically.'**
  String get smsPermissionMessage;

  /// No description provided for @allowSmsAccess.
  ///
  /// In en, this message translates to:
  /// **'Allow SMS Access'**
  String get allowSmsAccess;

  /// No description provided for @smsAccessExplanation.
  ///
  /// In en, this message translates to:
  /// **'We\'ll only read messages from mobile money numbers to track your transactions.'**
  String get smsAccessExplanation;

  /// No description provided for @batteryOptimizationTitle.
  ///
  /// In en, this message translates to:
  /// **'Battery Optimization'**
  String get batteryOptimizationTitle;

  /// No description provided for @batteryOptimizationMessage.
  ///
  /// In en, this message translates to:
  /// **'To ensure reliable operation, please disable battery optimization for Agent Kujukiza.'**
  String get batteryOptimizationMessage;

  /// No description provided for @enableBatteryOptimization.
  ///
  /// In en, this message translates to:
  /// **'Enable'**
  String get enableBatteryOptimization;

  /// No description provided for @batteryOptimizationExplanation.
  ///
  /// In en, this message translates to:
  /// **'This allows the app to run in the background and process transactions.'**
  String get batteryOptimizationExplanation;

  /// No description provided for @setupCompleteTitle.
  ///
  /// In en, this message translates to:
  /// **'Setup Complete!'**
  String get setupCompleteTitle;

  /// No description provided for @setupCompleteMessage.
  ///
  /// In en, this message translates to:
  /// **'You\'re all set to start tracking your mobile money transactions.'**
  String get setupCompleteMessage;

  /// No description provided for @goToDashboard.
  ///
  /// In en, this message translates to:
  /// **'Go to Dashboard'**
  String get goToDashboard;

  /// No description provided for @continueText.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continueText;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @ok.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// No description provided for @error.
  ///
  /// In en, this message translates to:
  /// **'Error'**
  String get error;

  /// No description provided for @success.
  ///
  /// In en, this message translates to:
  /// **'Success'**
  String get success;

  /// No description provided for @loading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get loading;

  /// No description provided for @currentFloat.
  ///
  /// In en, this message translates to:
  /// **'Current Float'**
  String get currentFloat;

  /// No description provided for @floatOk.
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get floatOk;

  /// No description provided for @lowFloat.
  ///
  /// In en, this message translates to:
  /// **'Low Float!'**
  String get lowFloat;

  /// No description provided for @deposits.
  ///
  /// In en, this message translates to:
  /// **'Deposits'**
  String get deposits;

  /// No description provided for @withdrawals.
  ///
  /// In en, this message translates to:
  /// **'Withdrawals'**
  String get withdrawals;

  /// No description provided for @floatTopUp.
  ///
  /// In en, this message translates to:
  /// **'Float Top-Up'**
  String get floatTopUp;

  /// No description provided for @netChange.
  ///
  /// In en, this message translates to:
  /// **'Net'**
  String get netChange;

  /// No description provided for @commission.
  ///
  /// In en, this message translates to:
  /// **'Commission'**
  String get commission;

  /// No description provided for @recentTransactions.
  ///
  /// In en, this message translates to:
  /// **'Recent Transactions'**
  String get recentTransactions;

  /// No description provided for @viewAllTransactions.
  ///
  /// In en, this message translates to:
  /// **'View All Transactions'**
  String get viewAllTransactions;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @transactions.
  ///
  /// In en, this message translates to:
  /// **'T-List'**
  String get transactions;

  /// No description provided for @export.
  ///
  /// In en, this message translates to:
  /// **'Export'**
  String get export;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @dashboard.
  ///
  /// In en, this message translates to:
  /// **'Dashboard'**
  String get dashboard;

  /// No description provided for @noTransactionsYet.
  ///
  /// In en, this message translates to:
  /// **'No transactions yet'**
  String get noTransactionsYet;

  /// No description provided for @refresh.
  ///
  /// In en, this message translates to:
  /// **'Refresh'**
  String get refresh;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @thisWeek.
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get thisWeek;

  /// No description provided for @thisMonth.
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get thisMonth;

  /// No description provided for @custom.
  ///
  /// In en, this message translates to:
  /// **'Custom'**
  String get custom;

  /// No description provided for @floatAsOf.
  ///
  /// In en, this message translates to:
  /// **'Float as of'**
  String get floatAsOf;

  /// No description provided for @floatStatus.
  ///
  /// In en, this message translates to:
  /// **'Float Status'**
  String get floatStatus;

  /// No description provided for @sufficient.
  ///
  /// In en, this message translates to:
  /// **'Sufficient'**
  String get sufficient;

  /// No description provided for @lowBalance.
  ///
  /// In en, this message translates to:
  /// **'Low Balance'**
  String get lowBalance;

  /// No description provided for @lastUpdated.
  ///
  /// In en, this message translates to:
  /// **'Last updated'**
  String get lastUpdated;

  /// No description provided for @selectPeriod.
  ///
  /// In en, this message translates to:
  /// **'Select Period'**
  String get selectPeriod;

  /// No description provided for @startingFloat.
  ///
  /// In en, this message translates to:
  /// **'Starting Float'**
  String get startingFloat;

  /// No description provided for @dailyRevenue.
  ///
  /// In en, this message translates to:
  /// **'Daily Revenue'**
  String get dailyRevenue;

  /// No description provided for @extraAmount.
  ///
  /// In en, this message translates to:
  /// **'Extra'**
  String get extraAmount;

  /// No description provided for @checkBalance.
  ///
  /// In en, this message translates to:
  /// **'Check Balance'**
  String get checkBalance;

  /// No description provided for @missing.
  ///
  /// In en, this message translates to:
  /// **'missing'**
  String get missing;

  /// No description provided for @selectDate.
  ///
  /// In en, this message translates to:
  /// **'Select Date'**
  String get selectDate;

  /// No description provided for @dailyAccountingSummary.
  ///
  /// In en, this message translates to:
  /// **'Daily Accounting Summary'**
  String get dailyAccountingSummary;

  /// No description provided for @startingFloatToday.
  ///
  /// In en, this message translates to:
  /// **'Starting Float for Today'**
  String get startingFloatToday;

  /// No description provided for @startingFloatDescription.
  ///
  /// In en, this message translates to:
  /// **'Money you began the day with'**
  String get startingFloatDescription;

  /// No description provided for @moneyEarnedSoFar.
  ///
  /// In en, this message translates to:
  /// **'Money Earned So Far'**
  String get moneyEarnedSoFar;

  /// No description provided for @dailyRevenueDescription.
  ///
  /// In en, this message translates to:
  /// **'Commission from transactions today'**
  String get dailyRevenueDescription;

  /// No description provided for @floatIncreaseToday.
  ///
  /// In en, this message translates to:
  /// **'Float Increase for Today'**
  String get floatIncreaseToday;

  /// No description provided for @floatIncreaseDescription.
  ///
  /// In en, this message translates to:
  /// **'How much your float has grown'**
  String get floatIncreaseDescription;

  /// No description provided for @accountingFormula.
  ///
  /// In en, this message translates to:
  /// **'Starting + Earned = Expected Float'**
  String get accountingFormula;

  /// No description provided for @switchLanguage.
  ///
  /// In en, this message translates to:
  /// **'Switch Language'**
  String get switchLanguage;

  /// No description provided for @languageSwitched.
  ///
  /// In en, this message translates to:
  /// **'Language switched successfully'**
  String get languageSwitched;

  /// No description provided for @search.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// No description provided for @filter.
  ///
  /// In en, this message translates to:
  /// **'Filter'**
  String get filter;

  /// No description provided for @searchByNameOrAmount.
  ///
  /// In en, this message translates to:
  /// **'Search by name or amount'**
  String get searchByNameOrAmount;

  /// No description provided for @noMatchesFound.
  ///
  /// In en, this message translates to:
  /// **'No matches found'**
  String get noMatchesFound;

  /// No description provided for @clearAll.
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get clearAll;

  /// No description provided for @subscription.
  ///
  /// In en, this message translates to:
  /// **'Subscription'**
  String get subscription;

  /// No description provided for @subscriptionActive.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get subscriptionActive;

  /// No description provided for @subscriptionExpired.
  ///
  /// In en, this message translates to:
  /// **'Expired'**
  String get subscriptionExpired;

  /// No description provided for @renewNow.
  ///
  /// In en, this message translates to:
  /// **'Renew Now'**
  String get renewNow;

  /// No description provided for @yongera.
  ///
  /// In en, this message translates to:
  /// **'Yongera'**
  String get yongera;

  /// No description provided for @daysRemaining.
  ///
  /// In en, this message translates to:
  /// **'days remaining'**
  String get daysRemaining;

  /// No description provided for @subscriptionExpiredMessage.
  ///
  /// In en, this message translates to:
  /// **'Your subscription has expired'**
  String get subscriptionExpiredMessage;

  /// No description provided for @renewSubscriptionMessage.
  ///
  /// In en, this message translates to:
  /// **'To continue using all features, renew your monthly subscription'**
  String get renewSubscriptionMessage;

  /// No description provided for @monthlyFee.
  ///
  /// In en, this message translates to:
  /// **'Monthly Fee: UGX 3,000'**
  String get monthlyFee;

  /// No description provided for @appInformation.
  ///
  /// In en, this message translates to:
  /// **'App Information'**
  String get appInformation;

  /// No description provided for @version.
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// No description provided for @platform.
  ///
  /// In en, this message translates to:
  /// **'Platform'**
  String get platform;

  /// No description provided for @dataStorage.
  ///
  /// In en, this message translates to:
  /// **'Data Storage'**
  String get dataStorage;

  /// No description provided for @localDevice.
  ///
  /// In en, this message translates to:
  /// **'Local Device'**
  String get localDevice;

  /// No description provided for @howToUse.
  ///
  /// In en, this message translates to:
  /// **'How to use Agent Kujukiza'**
  String get howToUse;

  /// No description provided for @enkola.
  ///
  /// In en, this message translates to:
  /// **'Enkola y\'okukozesa'**
  String get enkola;

  /// No description provided for @step1.
  ///
  /// In en, this message translates to:
  /// **'Allow SMS'**
  String get step1;

  /// No description provided for @step2.
  ///
  /// In en, this message translates to:
  /// **'Get Float'**
  String get step2;

  /// No description provided for @step3.
  ///
  /// In en, this message translates to:
  /// **'View Transactions'**
  String get step3;

  /// No description provided for @step1Description.
  ///
  /// In en, this message translates to:
  /// **'Grant SMS permissions to read transaction messages'**
  String get step1Description;

  /// No description provided for @step2Description.
  ///
  /// In en, this message translates to:
  /// **'Top up your mobile money float'**
  String get step2Description;

  /// No description provided for @step3Description.
  ///
  /// In en, this message translates to:
  /// **'Monitor your transactions and earnings'**
  String get step3Description;

  /// No description provided for @paymentMethods.
  ///
  /// In en, this message translates to:
  /// **'Payment Methods'**
  String get paymentMethods;

  /// No description provided for @mtnMobileMoney.
  ///
  /// In en, this message translates to:
  /// **'MTN Mobile Money'**
  String get mtnMobileMoney;

  /// No description provided for @airtelMoney.
  ///
  /// In en, this message translates to:
  /// **'Airtel Money'**
  String get airtelMoney;

  /// No description provided for @cashVoucher.
  ///
  /// In en, this message translates to:
  /// **'Cash Voucher'**
  String get cashVoucher;

  /// No description provided for @ussdPayment.
  ///
  /// In en, this message translates to:
  /// **'USSD Payment'**
  String get ussdPayment;

  /// No description provided for @selectPaymentMethod.
  ///
  /// In en, this message translates to:
  /// **'Select Payment Method'**
  String get selectPaymentMethod;

  /// No description provided for @processingPayment.
  ///
  /// In en, this message translates to:
  /// **'Processing Payment...'**
  String get processingPayment;

  /// No description provided for @paymentSuccessful.
  ///
  /// In en, this message translates to:
  /// **'Payment Successful!'**
  String get paymentSuccessful;

  /// No description provided for @paymentFailed.
  ///
  /// In en, this message translates to:
  /// **'Payment Failed'**
  String get paymentFailed;

  /// No description provided for @subscriptionRenewed.
  ///
  /// In en, this message translates to:
  /// **'Your subscription has been renewed until'**
  String get subscriptionRenewed;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @debugSection.
  ///
  /// In en, this message translates to:
  /// **'Debug Section'**
  String get debugSection;

  /// No description provided for @expireSubscription.
  ///
  /// In en, this message translates to:
  /// **'Expire Subscription'**
  String get expireSubscription;

  /// No description provided for @renewSubscription.
  ///
  /// In en, this message translates to:
  /// **'Renew Subscription'**
  String get renewSubscription;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @setupYourFloat.
  ///
  /// In en, this message translates to:
  /// **'Setup Your Float'**
  String get setupYourFloat;

  /// No description provided for @enterCurrentBalancePrompt.
  ///
  /// In en, this message translates to:
  /// **'Enter your current balance on MTN and Airtel mobile money agent accounts to begin tracking your daily float.'**
  String get enterCurrentBalancePrompt;

  /// No description provided for @mtnFloat.
  ///
  /// In en, this message translates to:
  /// **'MTN Float (UGX)'**
  String get mtnFloat;

  /// No description provided for @airtelFloat.
  ///
  /// In en, this message translates to:
  /// **'Airtel Float (UGX)'**
  String get airtelFloat;

  /// No description provided for @floatSetupInfo.
  ///
  /// In en, this message translates to:
  /// **'This information is stored securely on your device and helps track your daily earnings and float changes.'**
  String get floatSetupInfo;

  /// No description provided for @continueToApp.
  ///
  /// In en, this message translates to:
  /// **'Continue to App'**
  String get continueToApp;

  /// No description provided for @pleaseEnterAmount.
  ///
  /// In en, this message translates to:
  /// **'Please enter an amount'**
  String get pleaseEnterAmount;

  /// No description provided for @pleaseEnterValidAmount.
  ///
  /// In en, this message translates to:
  /// **'Please enter a valid amount'**
  String get pleaseEnterValidAmount;

  /// No description provided for @floatSetupError.
  ///
  /// In en, this message translates to:
  /// **'Failed to setup float. Please try again.'**
  String get floatSetupError;

  /// No description provided for @mtn.
  ///
  /// In en, this message translates to:
  /// **'MTN'**
  String get mtn;

  /// No description provided for @airtel.
  ///
  /// In en, this message translates to:
  /// **'Airtel'**
  String get airtel;

  /// No description provided for @xtraFloatActive.
  ///
  /// In en, this message translates to:
  /// **'XtraFloat Active'**
  String get xtraFloatActive;

  /// No description provided for @xtraFloatInactive.
  ///
  /// In en, this message translates to:
  /// **'XtraFloat Inactive'**
  String get xtraFloatInactive;

  /// No description provided for @amountHint.
  ///
  /// In en, this message translates to:
  /// **'0'**
  String get amountHint;

  /// No description provided for @ugxPrefix.
  ///
  /// In en, this message translates to:
  /// **'UGX '**
  String get ugxPrefix;

  /// No description provided for @fromStartingFloat.
  ///
  /// In en, this message translates to:
  /// **'from starting float'**
  String get fromStartingFloat;

  /// No description provided for @justNow.
  ///
  /// In en, this message translates to:
  /// **'just now'**
  String get justNow;

  /// No description provided for @minutesAgo.
  ///
  /// In en, this message translates to:
  /// **'m ago'**
  String get minutesAgo;

  /// No description provided for @hoursAgo.
  ///
  /// In en, this message translates to:
  /// **'h ago'**
  String get hoursAgo;

  /// No description provided for @daysAgo.
  ///
  /// In en, this message translates to:
  /// **'d ago'**
  String get daysAgo;

  /// No description provided for @dailyMetrics.
  ///
  /// In en, this message translates to:
  /// **'Daily Metrics'**
  String get dailyMetrics;

  /// No description provided for @commissionLabel.
  ///
  /// In en, this message translates to:
  /// **'Commission:'**
  String get commissionLabel;

  /// No description provided for @balanceLabel.
  ///
  /// In en, this message translates to:
  /// **'Bal:'**
  String get balanceLabel;

  /// No description provided for @balanceUnknown.
  ///
  /// In en, this message translates to:
  /// **'Bal: –––'**
  String get balanceUnknown;

  /// No description provided for @todayLabel.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get todayLabel;

  /// No description provided for @yesterdayLabel.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterdayLabel;

  /// No description provided for @transactionLabel.
  ///
  /// In en, this message translates to:
  /// **'Transaction'**
  String get transactionLabel;

  /// No description provided for @fullSmsLabel.
  ///
  /// In en, this message translates to:
  /// **'📄 Full SMS:'**
  String get fullSmsLabel;

  /// No description provided for @transactionIdLabel.
  ///
  /// In en, this message translates to:
  /// **'↳ Transaction ID:'**
  String get transactionIdLabel;

  /// No description provided for @commissionDetailsLabel.
  ///
  /// In en, this message translates to:
  /// **'💰 Commission:'**
  String get commissionDetailsLabel;

  /// No description provided for @close.
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'lg'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'lg':
      return AppLocalizationsLg();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
