enum OnboardingStep {
  languageSelection,
  smsPermission,
  batteryOptimization,
  setupComplete,
  completed;

  bool get isPermissionStep => this == smsPermission || this == batteryOptimization;
  
  OnboardingStep? get nextStep {
    switch (this) {
      case OnboardingStep.languageSelection:
        return OnboardingStep.smsPermission;
      case OnboardingStep.smsPermission:
        return OnboardingStep.batteryOptimization;
      case OnboardingStep.batteryOptimization:
        return OnboardingStep.setupComplete;
      case OnboardingStep.setupComplete:
        return OnboardingStep.completed;
      case OnboardingStep.completed:
        return null;
    }
  }

  OnboardingStep? get previousStep {
    switch (this) {
      case OnboardingStep.languageSelection:
        return null;
      case OnboardingStep.smsPermission:
        return OnboardingStep.languageSelection;
      case OnboardingStep.batteryOptimization:
        return OnboardingStep.smsPermission;
      case OnboardingStep.setupComplete:
        return OnboardingStep.batteryOptimization;
      case OnboardingStep.completed:
        return OnboardingStep.setupComplete;
    }
  }
}

class OnboardingState {
  final OnboardingStep currentStep;
  final bool hasGrantedSms;
  final bool hasGrantedBattery;
  final bool hasSelectedLanguage;
  final bool isOnboardingComplete;
  final DateTime? lastUpdated;

  const OnboardingState({
    this.currentStep = OnboardingStep.languageSelection,
    this.hasGrantedSms = false,
    this.hasGrantedBattery = false,
    this.hasSelectedLanguage = false,
    this.isOnboardingComplete = false,
    this.lastUpdated,
  });

  bool get canProceedToNext {
    switch (currentStep) {
      case OnboardingStep.languageSelection:
        return hasSelectedLanguage;
      case OnboardingStep.smsPermission:
        return hasGrantedSms;
      case OnboardingStep.batteryOptimization:
        return hasGrantedBattery;
      case OnboardingStep.setupComplete:
        return true;
      case OnboardingStep.completed:
        return true;
    }
  }

  bool get allPermissionsGranted => hasGrantedSms && hasGrantedBattery;
  bool get allStepsCompleted => hasSelectedLanguage && allPermissionsGranted;

  OnboardingState copyWith({
    OnboardingStep? currentStep,
    bool? hasGrantedSms,
    bool? hasGrantedBattery,
    bool? hasSelectedLanguage,
    bool? isOnboardingComplete,
    DateTime? lastUpdated,
  }) {
    return OnboardingState(
      currentStep: currentStep ?? this.currentStep,
      hasGrantedSms: hasGrantedSms ?? this.hasGrantedSms,
      hasGrantedBattery: hasGrantedBattery ?? this.hasGrantedBattery,
      hasSelectedLanguage: hasSelectedLanguage ?? this.hasSelectedLanguage,
      isOnboardingComplete: isOnboardingComplete ?? this.isOnboardingComplete,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'currentStep': currentStep.index,
      'hasGrantedSms': hasGrantedSms,
      'hasGrantedBattery': hasGrantedBattery,
      'hasSelectedLanguage': hasSelectedLanguage,
      'isOnboardingComplete': isOnboardingComplete,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  factory OnboardingState.fromJson(Map<String, dynamic> json) {
    return OnboardingState(
      currentStep: OnboardingStep.values[json['currentStep'] ?? 0],
      hasGrantedSms: json['hasGrantedSms'] ?? false,
      hasGrantedBattery: json['hasGrantedBattery'] ?? false,
      hasSelectedLanguage: json['hasSelectedLanguage'] ?? false,
      isOnboardingComplete: json['isOnboardingComplete'] ?? false,
      lastUpdated: json['lastUpdated'] != null 
          ? DateTime.parse(json['lastUpdated'])
          : null,
    );
  }

  @override
  String toString() {
    return 'OnboardingState(currentStep: $currentStep, hasGrantedSms: $hasGrantedSms, hasGrantedBattery: $hasGrantedBattery, hasSelectedLanguage: $hasSelectedLanguage, isOnboardingComplete: $isOnboardingComplete)';
  }
}

enum PermissionDenialReason {
  userDenied,
  permanentlyDenied,
  restricted,
  unknown;

  String get userFriendlyMessage {
    switch (this) {
      case PermissionDenialReason.userDenied:
        return 'Permission was denied. Please try again.';
      case PermissionDenialReason.permanentlyDenied:
        return 'Permission was permanently denied. Please enable it in Settings.';
      case PermissionDenialReason.restricted:
        return 'Permission is restricted on this device.';
      case PermissionDenialReason.unknown:
        return 'Unable to grant permission. Please try again.';
    }
  }
}

class PermissionResult {
  final bool isGranted;
  final PermissionDenialReason? denialReason;
  final bool shouldShowRationale;

  const PermissionResult({
    required this.isGranted,
    this.denialReason,
    this.shouldShowRationale = false,
  });

  factory PermissionResult.granted() {
    return const PermissionResult(isGranted: true);
  }

  factory PermissionResult.denied({
    PermissionDenialReason reason = PermissionDenialReason.userDenied,
    bool shouldShowRationale = false,
  }) {
    return PermissionResult(
      isGranted: false,
      denialReason: reason,
      shouldShowRationale: shouldShowRationale,
    );
  }
}
