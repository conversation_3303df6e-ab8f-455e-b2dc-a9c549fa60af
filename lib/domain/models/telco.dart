import 'package:flutter/material.dart';

enum Telco {
  mtn('MTN', 'mtn'),
  airtel('Airtel', 'airtel');

  const Telco(this.displayName, this.code);

  final String displayName;
  final String code;

  /// Get telco brand color
  Color get brandColor {
    switch (this) {
      case Telco.mtn:
        return const Color(0xFFFFD700); // MTN Yellow
      case Telco.airtel:
        return const Color(0xFFE60012); // Airtel Red
    }
  }

  /// Get telco secondary color (for backgrounds)
  Color get secondaryColor {
    switch (this) {
      case Telco.mtn:
        return const Color(0xFFFFF8DC); // Light yellow
      case Telco.airtel:
        return const Color(0xFFFFE4E1); // Light red
    }
  }

  /// Get telco icon/emoji
  String get icon {
    switch (this) {
      case Telco.mtn:
        return '🟨';
      case Telco.airtel:
        return '🟥';
    }
  }

  /// Parse telco from string
  static Telco? fromString(String value) {
    final lowerValue = value.toLowerCase();
    for (final telco in Telco.values) {
      if (telco.code == lowerValue ||
          telco.displayName.toLowerCase() == lowerValue) {
        return telco;
      }
    }
    return null;
  }
}
