import 'package:agent_kujukiza/domain/models/telco.dart';

enum TransactionType { 
  deposit, 
  withdrawal, 
  floatTopUp,
  commission, // For commission earnings
}

class TransactionEnhanced {
  final String id;
  final TransactionType type;
  final Telco telco;
  final double amount;
  final double? newBalance;
  final String counterparty;
  final DateTime timestamp;
  final String? rawSms;
  final double? commission; // Commission earned from this transaction

  const TransactionEnhanced({
    required this.id,
    required this.type,
    required this.telco,
    required this.amount,
    this.newBalance,
    required this.counterparty,
    required this.timestamp,
    this.rawSms,
    this.commission,
  });

  /// Check if transaction is positive (adds money)
  bool get isPositive => type == TransactionType.deposit || 
                        type == TransactionType.floatTopUp ||
                        type == TransactionType.commission;

  /// Get display amount (with sign)
  String get displayAmount {
    final sign = isPositive ? '+' : '-';
    return '$sign UGX ${amount.toStringAsFixed(0).replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'), 
      (Match m) => '${m[1]},',
    )}';
  }

  /// Get time formatted as HH:MM
  String get timeFormatted {
    return '${timestamp.hour.toString().padLeft(2, '0')}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  /// Create copy with updated values
  TransactionEnhanced copyWith({
    String? id,
    TransactionType? type,
    Telco? telco,
    double? amount,
    double? newBalance,
    String? counterparty,
    DateTime? timestamp,
    String? rawSms,
    double? commission,
  }) {
    return TransactionEnhanced(
      id: id ?? this.id,
      type: type ?? this.type,
      telco: telco ?? this.telco,
      amount: amount ?? this.amount,
      newBalance: newBalance ?? this.newBalance,
      counterparty: counterparty ?? this.counterparty,
      timestamp: timestamp ?? this.timestamp,
      rawSms: rawSms ?? this.rawSms,
      commission: commission ?? this.commission,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.index,
      'telco': telco.code,
      'amount': amount,
      'newBalance': newBalance,
      'counterparty': counterparty,
      'timestamp': timestamp.toIso8601String(),
      'rawSms': rawSms,
      'commission': commission,
    };
  }

  /// Create from JSON
  factory TransactionEnhanced.fromJson(Map<String, dynamic> json) {
    return TransactionEnhanced(
      id: json['id'] as String,
      type: TransactionType.values[json['type'] as int],
      telco: Telco.fromString(json['telco']) ?? Telco.mtn,
      amount: (json['amount'] as num).toDouble(),
      newBalance: (json['newBalance'] as num?)?.toDouble(),
      counterparty: json['counterparty'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      rawSms: json['rawSms'] as String?,
      commission: (json['commission'] as num?)?.toDouble(),
    );
  }

  @override
  String toString() {
    return 'TransactionEnhanced(id: $id, type: $type, telco: $telco, amount: $amount, counterparty: $counterparty)';
  }
}
