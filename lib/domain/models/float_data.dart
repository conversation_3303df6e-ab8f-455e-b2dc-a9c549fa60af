import 'package:agent_kujukiza/domain/models/telco.dart';

class FloatData {
  final Telco telco;
  final double currentFloat;
  final double startingFloat; // Float at beginning of day
  final double dailyDeposits;
  final double dailyWithdrawals;
  final double dailyFloatTopUps;
  final double dailyCommission;
  final bool isXtraFloatActive; // Only for MTN
  final DateTime lastUpdated;

  const FloatData({
    required this.telco,
    required this.currentFloat,
    required this.startingFloat,
    this.dailyDeposits = 0.0,
    this.dailyWithdrawals = 0.0,
    this.dailyFloatTopUps = 0.0,
    this.dailyCommission = 0.0,
    this.isXtraFloatActive = false,
    required this.lastUpdated,
  });

  /// Calculate net change from starting float
  double get netChange => currentFloat - startingFloat;

  /// Calculate total daily activity
  double get totalDailyActivity => dailyDeposits + dailyWithdrawals + dailyFloatTopUps;

  /// Check if float is below threshold
  bool isFloatLow(double threshold) => currentFloat < threshold;

  /// Create copy with updated values
  FloatData copyWith({
    Telco? telco,
    double? currentFloat,
    double? startingFloat,
    double? dailyDeposits,
    double? dailyWithdrawals,
    double? dailyFloatTopUps,
    double? dailyCommission,
    bool? isXtraFloatActive,
    DateTime? lastUpdated,
  }) {
    return FloatData(
      telco: telco ?? this.telco,
      currentFloat: currentFloat ?? this.currentFloat,
      startingFloat: startingFloat ?? this.startingFloat,
      dailyDeposits: dailyDeposits ?? this.dailyDeposits,
      dailyWithdrawals: dailyWithdrawals ?? this.dailyWithdrawals,
      dailyFloatTopUps: dailyFloatTopUps ?? this.dailyFloatTopUps,
      dailyCommission: dailyCommission ?? this.dailyCommission,
      isXtraFloatActive: isXtraFloatActive ?? this.isXtraFloatActive,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'telco': telco.code,
      'currentFloat': currentFloat,
      'startingFloat': startingFloat,
      'dailyDeposits': dailyDeposits,
      'dailyWithdrawals': dailyWithdrawals,
      'dailyFloatTopUps': dailyFloatTopUps,
      'dailyCommission': dailyCommission,
      'isXtraFloatActive': isXtraFloatActive,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  /// Create from JSON
  factory FloatData.fromJson(Map<String, dynamic> json) {
    return FloatData(
      telco: Telco.fromString(json['telco']) ?? Telco.mtn,
      currentFloat: (json['currentFloat'] as num?)?.toDouble() ?? 0.0,
      startingFloat: (json['startingFloat'] as num?)?.toDouble() ?? 0.0,
      dailyDeposits: (json['dailyDeposits'] as num?)?.toDouble() ?? 0.0,
      dailyWithdrawals: (json['dailyWithdrawals'] as num?)?.toDouble() ?? 0.0,
      dailyFloatTopUps: (json['dailyFloatTopUps'] as num?)?.toDouble() ?? 0.0,
      dailyCommission: (json['dailyCommission'] as num?)?.toDouble() ?? 0.0,
      isXtraFloatActive: json['isXtraFloatActive'] as bool? ?? false,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }

  @override
  String toString() {
    return 'FloatData(telco: $telco, currentFloat: $currentFloat, startingFloat: $startingFloat, dailyCommission: $dailyCommission, isXtraFloatActive: $isXtraFloatActive)';
  }
}
