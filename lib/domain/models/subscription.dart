class Subscription {
  final DateTime? expiryDate;
  final bool isActive;
  final int daysRemaining;
  final double monthlyFee;
  final DateTime? lastPaymentDate;
  final String? paymentMethod;

  const Subscription({
    this.expiryDate,
    required this.isActive,
    required this.daysRemaining,
    this.monthlyFee = 3000.0,
    this.lastPaymentDate,
    this.paymentMethod,
  });

  factory Subscription.expired() {
    return const Subscription(
      isActive: false,
      daysRemaining: 0,
    );
  }

  factory Subscription.active({
    required DateTime expiryDate,
    DateTime? lastPaymentDate,
    String? paymentMethod,
  }) {
    final now = DateTime.now();
    final daysRemaining = expiryDate.difference(now).inDays;
    
    return Subscription(
      expiryDate: expiryDate,
      isActive: daysRemaining > 0,
      daysRemaining: daysRemaining > 0 ? daysRemaining : 0,
      lastPaymentDate: lastPaymentDate,
      paymentMethod: paymentMethod,
    );
  }

  bool get isExpiringSoon => isActive && daysRemaining <= 1;
  bool get isExpired => !isActive || daysRemaining <= 0;

  String get statusText => isActive ? 'Active' : 'Expired';
  
  Map<String, dynamic> toJson() {
    return {
      'expiryDate': expiryDate?.toIso8601String(),
      'isActive': isActive,
      'daysRemaining': daysRemaining,
      'monthlyFee': monthlyFee,
      'lastPaymentDate': lastPaymentDate?.toIso8601String(),
      'paymentMethod': paymentMethod,
    };
  }

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      expiryDate: json['expiryDate'] != null 
          ? DateTime.parse(json['expiryDate'])
          : null,
      isActive: json['isActive'] ?? false,
      daysRemaining: json['daysRemaining'] ?? 0,
      monthlyFee: json['monthlyFee']?.toDouble() ?? 3000.0,
      lastPaymentDate: json['lastPaymentDate'] != null
          ? DateTime.parse(json['lastPaymentDate'])
          : null,
      paymentMethod: json['paymentMethod'],
    );
  }

  Subscription copyWith({
    DateTime? expiryDate,
    bool? isActive,
    int? daysRemaining,
    double? monthlyFee,
    DateTime? lastPaymentDate,
    String? paymentMethod,
  }) {
    return Subscription(
      expiryDate: expiryDate ?? this.expiryDate,
      isActive: isActive ?? this.isActive,
      daysRemaining: daysRemaining ?? this.daysRemaining,
      monthlyFee: monthlyFee ?? this.monthlyFee,
      lastPaymentDate: lastPaymentDate ?? this.lastPaymentDate,
      paymentMethod: paymentMethod ?? this.paymentMethod,
    );
  }
}

enum PaymentMethod {
  mtnMobileMoney('MTN Mobile Money'),
  airtelMoney('Airtel Money'),
  cashVoucher('Cash Voucher'),
  ussd('USSD');

  const PaymentMethod(this.displayName);
  final String displayName;
}
