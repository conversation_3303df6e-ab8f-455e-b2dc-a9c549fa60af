import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:crypto/crypto.dart';
import 'package:uuid/uuid.dart';

class DeviceIdService {
  static const String _deviceIdKey = 'agent_device_id';
  static const String _deviceInfoKey = 'device_info_hash';
  static const String _installationIdKey = 'installation_id';
  
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  static DeviceInfoPlugin? _deviceInfo;
  static String? _cachedDeviceId;
  static String? _cachedInstallationId;

  /// Initialize the service
  static Future<void> initialize() async {
    debugPrint('Initializing DeviceIdService...');
    _deviceInfo = DeviceInfoPlugin();
    
    // Pre-load device ID
    await getDeviceId();
    debugPrint('DeviceIdService initialized');
  }

  /// Get or create a persistent device ID
  static Future<String> getDeviceId() async {
    if (_cachedDeviceId != null) {
      return _cachedDeviceId!;
    }

    try {
      // Try to get existing device ID
      String? existingId = await _secureStorage.read(key: _deviceIdKey);
      
      if (existingId != null && existingId.isNotEmpty) {
        debugPrint('Found existing device ID: ${existingId.substring(0, 8)}...');
        _cachedDeviceId = existingId;
        return existingId;
      }

      // Generate new device ID
      final newDeviceId = await _generateDeviceId();
      await _secureStorage.write(key: _deviceIdKey, value: newDeviceId);
      
      debugPrint('Generated new device ID: ${newDeviceId.substring(0, 8)}...');
      _cachedDeviceId = newDeviceId;
      return newDeviceId;
      
    } catch (e) {
      debugPrint('Error getting device ID: $e');
      // Fallback to a UUID if secure storage fails
      final fallbackId = const Uuid().v4();
      _cachedDeviceId = fallbackId;
      return fallbackId;
    }
  }

  /// Generate a unique device ID based on device characteristics
  static Future<String> _generateDeviceId() async {
    try {
      final deviceInfo = await _getDeviceInfo();
      final uuid = const Uuid().v4();
      
      // Combine device info with UUID for uniqueness
      final combined = '$deviceInfo-$uuid';
      final bytes = utf8.encode(combined);
      final digest = sha256.convert(bytes);
      
      return digest.toString();
    } catch (e) {
      debugPrint('Error generating device ID: $e');
      // Fallback to pure UUID
      return const Uuid().v4();
    }
  }

  /// Get device information for fingerprinting
  static Future<String> _getDeviceInfo() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo!.androidInfo;
        return '${androidInfo.model}-${androidInfo.brand}-${androidInfo.device}-${androidInfo.id}';
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo!.iosInfo;
        return '${iosInfo.model}-${iosInfo.name}-${iosInfo.systemVersion}-${iosInfo.identifierForVendor}';
      }
    } catch (e) {
      debugPrint('Error getting device info: $e');
    }
    
    return 'unknown-device';
  }

  /// Get installation ID (changes on reinstall)
  static Future<String> getInstallationId() async {
    if (_cachedInstallationId != null) {
      return _cachedInstallationId!;
    }

    try {
      String? existingId = await _secureStorage.read(key: _installationIdKey);
      
      if (existingId != null && existingId.isNotEmpty) {
        _cachedInstallationId = existingId;
        return existingId;
      }

      // Generate new installation ID
      final newInstallationId = const Uuid().v4();
      await _secureStorage.write(key: _installationIdKey, value: newInstallationId);
      
      debugPrint('Generated new installation ID: ${newInstallationId.substring(0, 8)}...');
      _cachedInstallationId = newInstallationId;
      return newInstallationId;
      
    } catch (e) {
      debugPrint('Error getting installation ID: $e');
      final fallbackId = const Uuid().v4();
      _cachedInstallationId = fallbackId;
      return fallbackId;
    }
  }

  /// Check if this is a fresh installation
  static Future<bool> isFreshInstallation() async {
    try {
      final deviceId = await _secureStorage.read(key: _deviceIdKey);
      final installationId = await _secureStorage.read(key: _installationIdKey);
      
      final isFresh = deviceId == null || installationId == null;
      debugPrint('Is fresh installation: $isFresh');
      return isFresh;
    } catch (e) {
      debugPrint('Error checking fresh installation: $e');
      return true; // Assume fresh if we can't determine
    }
  }

  /// Check if device has been tampered with (different device characteristics)
  static Future<bool> isDeviceTampered() async {
    try {
      final currentDeviceInfo = await _getDeviceInfo();
      final currentHash = sha256.convert(utf8.encode(currentDeviceInfo)).toString();
      
      final storedHash = await _secureStorage.read(key: _deviceInfoKey);
      
      if (storedHash == null) {
        // First time, store the hash
        await _secureStorage.write(key: _deviceInfoKey, value: currentHash);
        return false;
      }
      
      final isTampered = currentHash != storedHash;
      if (isTampered) {
        debugPrint('Device tampering detected!');
      }
      
      return isTampered;
    } catch (e) {
      debugPrint('Error checking device tampering: $e');
      return false; // Assume not tampered if we can't determine
    }
  }

  /// Get device information for display
  static Future<Map<String, String>> getDeviceInfoForDisplay() async {
    try {
      if (Platform.isAndroid) {
        final androidInfo = await _deviceInfo!.androidInfo;
        return {
          'Device': '${androidInfo.brand} ${androidInfo.model}',
          'Android Version': androidInfo.version.release,
          'SDK': androidInfo.version.sdkInt.toString(),
          'Device ID': (await getDeviceId()).substring(0, 8) + '...',
        };
      } else if (Platform.isIOS) {
        final iosInfo = await _deviceInfo!.iosInfo;
        return {
          'Device': '${iosInfo.name} ${iosInfo.model}',
          'iOS Version': iosInfo.systemVersion,
          'Device ID': (await getDeviceId()).substring(0, 8) + '...',
        };
      }
    } catch (e) {
      debugPrint('Error getting device info for display: $e');
    }
    
    return {
      'Device': 'Unknown',
      'Device ID': (await getDeviceId()).substring(0, 8) + '...',
    };
  }

  /// Reset device ID (for testing purposes only)
  static Future<void> resetDeviceId() async {
    debugPrint('Resetting device ID...');
    try {
      await _secureStorage.delete(key: _deviceIdKey);
      await _secureStorage.delete(key: _deviceInfoKey);
      await _secureStorage.delete(key: _installationIdKey);
      _cachedDeviceId = null;
      _cachedInstallationId = null;
      debugPrint('Device ID reset complete');
    } catch (e) {
      debugPrint('Error resetting device ID: $e');
    }
  }

  /// Clear all stored data (for testing/debugging)
  static Future<void> clearAll() async {
    debugPrint('Clearing all device data...');
    try {
      await _secureStorage.deleteAll();
      _cachedDeviceId = null;
      _cachedInstallationId = null;
      debugPrint('All device data cleared');
    } catch (e) {
      debugPrint('Error clearing device data: $e');
    }
  }

  /// Get all stored keys (for debugging)
  static Future<Map<String, String>> getAllStoredData() async {
    try {
      return await _secureStorage.readAll();
    } catch (e) {
      debugPrint('Error reading all stored data: $e');
      return {};
    }
  }
}
