import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:agent_kujukiza/domain/models/onboarding_state.dart';
import 'package:agent_kujukiza/core/storage/local_prefs.dart';

class PermissionService {
  static const Duration _permissionCheckCooldown = Duration(seconds: 2);
  static DateTime? _lastPermissionCheck;

  /// Check if SMS permission is granted
  static Future<bool> isSmsPermissionGranted() async {
    try {
      final status = await Permission.sms.status;
      final isGranted = status.isGranted;
      debugPrint('SMS permission status: $status (granted: $isGranted)');
      return isGranted;
    } catch (e) {
      debugPrint('Error checking SMS permission: $e');
      return false;
    }
  }

  /// Request SMS permission
  static Future<PermissionResult> requestSmsPermission() async {
    try {
      // Check if we're in cooldown period
      if (_isInCooldown()) {
        debugPrint('Permission request in cooldown period');
        return PermissionResult.denied(reason: PermissionDenialReason.unknown);
      }

      _lastPermissionCheck = DateTime.now();
      await LocalPrefs.saveLastPermissionCheck();

      final status = await Permission.sms.request();
      debugPrint('SMS permission request result: $status');

      switch (status) {
        case PermissionStatus.granted:
          return PermissionResult.granted();
        case PermissionStatus.denied:
          return PermissionResult.denied(
            reason: PermissionDenialReason.userDenied,
            shouldShowRationale: await Permission.sms.shouldShowRequestRationale,
          );
        case PermissionStatus.permanentlyDenied:
          return PermissionResult.denied(
            reason: PermissionDenialReason.permanentlyDenied,
          );
        case PermissionStatus.restricted:
          return PermissionResult.denied(
            reason: PermissionDenialReason.restricted,
          );
        default:
          return PermissionResult.denied(
            reason: PermissionDenialReason.unknown,
          );
      }
    } catch (e) {
      debugPrint('Error requesting SMS permission: $e');
      return PermissionResult.denied(reason: PermissionDenialReason.unknown);
    }
  }

  /// Check if battery optimization is disabled (app can run in background)
  static Future<bool> isBatteryOptimizationDisabled() async {
    try {
      // On Android, check if battery optimization is ignored
      final status = await Permission.ignoreBatteryOptimizations.status;
      final isGranted = status.isGranted;
      debugPrint('Battery optimization status: $status (disabled: $isGranted)');
      return isGranted;
    } catch (e) {
      debugPrint('Error checking battery optimization: $e');
      return false;
    }
  }

  /// Request to disable battery optimization
  static Future<PermissionResult> requestBatteryOptimizationDisable() async {
    try {
      // Check if we're in cooldown period
      if (_isInCooldown()) {
        debugPrint('Permission request in cooldown period');
        return PermissionResult.denied(reason: PermissionDenialReason.unknown);
      }

      _lastPermissionCheck = DateTime.now();
      await LocalPrefs.saveLastPermissionCheck();

      final status = await Permission.ignoreBatteryOptimizations.request();
      debugPrint('Battery optimization request result: $status');

      switch (status) {
        case PermissionStatus.granted:
          return PermissionResult.granted();
        case PermissionStatus.denied:
          return PermissionResult.denied(
            reason: PermissionDenialReason.userDenied,
            shouldShowRationale: await Permission.ignoreBatteryOptimizations.shouldShowRequestRationale,
          );
        case PermissionStatus.permanentlyDenied:
          return PermissionResult.denied(
            reason: PermissionDenialReason.permanentlyDenied,
          );
        case PermissionStatus.restricted:
          return PermissionResult.denied(
            reason: PermissionDenialReason.restricted,
          );
        default:
          return PermissionResult.denied(
            reason: PermissionDenialReason.unknown,
          );
      }
    } catch (e) {
      debugPrint('Error requesting battery optimization disable: $e');
      return PermissionResult.denied(reason: PermissionDenialReason.unknown);
    }
  }

  /// Check all required permissions at once
  static Future<Map<String, bool>> checkAllPermissions() async {
    try {
      final smsGranted = await isSmsPermissionGranted();
      final batteryGranted = await isBatteryOptimizationDisabled();

      final results = {
        'sms': smsGranted,
        'battery': batteryGranted,
      };

      debugPrint('All permissions check: $results');
      return results;
    } catch (e) {
      debugPrint('Error checking all permissions: $e');
      return {
        'sms': false,
        'battery': false,
      };
    }
  }

  /// Open app settings for manual permission management
  static Future<bool> openAppSettings() async {
    try {
      final opened = await openAppSettings();
      debugPrint('Opened app settings: $opened');
      return opened;
    } catch (e) {
      debugPrint('Error opening app settings: $e');
      return false;
    }
  }

  /// Check if we should show rationale for a specific permission
  static Future<bool> shouldShowSmsRationale() async {
    try {
      return await Permission.sms.shouldShowRequestRationale;
    } catch (e) {
      debugPrint('Error checking SMS rationale: $e');
      return false;
    }
  }

  /// Check if we should show rationale for battery optimization
  static Future<bool> shouldShowBatteryRationale() async {
    try {
      return await Permission.ignoreBatteryOptimizations.shouldShowRequestRationale;
    } catch (e) {
      debugPrint('Error checking battery rationale: $e');
      return false;
    }
  }

  /// Refresh permission status (call when app resumes)
  static Future<Map<String, bool>> refreshPermissionStatus() async {
    debugPrint('Refreshing permission status...');
    
    // Clear cooldown when refreshing
    _lastPermissionCheck = null;
    
    return await checkAllPermissions();
  }

  /// Check if we're in cooldown period to prevent rapid permission requests
  static bool _isInCooldown() {
    if (_lastPermissionCheck == null) return false;
    
    final timeSinceLastCheck = DateTime.now().difference(_lastPermissionCheck!);
    return timeSinceLastCheck < _permissionCheckCooldown;
  }

  /// Get user-friendly permission status message
  static String getPermissionStatusMessage(String permissionType, bool isGranted) {
    if (isGranted) {
      return '$permissionType permission is granted';
    } else {
      switch (permissionType.toLowerCase()) {
        case 'sms':
          return 'SMS permission is required to read transaction messages';
        case 'battery':
          return 'Battery optimization must be disabled for background operation';
        default:
          return '$permissionType permission is required';
      }
    }
  }

  /// Check if all critical permissions are granted
  static Future<bool> areAllCriticalPermissionsGranted() async {
    final permissions = await checkAllPermissions();
    final allGranted = permissions.values.every((granted) => granted);
    debugPrint('All critical permissions granted: $allGranted');
    return allGranted;
  }

  /// Initialize permission service
  static Future<void> initialize() async {
    debugPrint('Initializing PermissionService...');
    
    // Load last permission check time
    final lastCheck = await LocalPrefs.loadLastPermissionCheck();
    if (lastCheck != null) {
      _lastPermissionCheck = lastCheck;
    }
    
    // Do initial permission check
    await checkAllPermissions();
    
    debugPrint('PermissionService initialized');
  }
}
