import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:agent_kujukiza/domain/models/telco.dart';
import 'package:agent_kujukiza/domain/models/float_data.dart';
import 'package:agent_kujukiza/domain/models/transaction_enhanced.dart';

class FloatService {
  static const String _hasInitializedFloatKey = 'has_initialized_float';
  static const String _mtnFloatKey = 'mtn_float_data';
  static const String _airtelFloatKey = 'airtel_float_data';
  static const String _transactionsKey = 'transactions_enhanced';
  
  static SharedPreferences? _prefs;

  /// Initialize the service
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Ensure preferences are initialized
  static Future<SharedPreferences> get _instance async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }

  // Float Initialization Management

  /// Check if float has been initialized
  static Future<bool> hasInitializedFloat() async {
    try {
      final prefs = await _instance;
      final hasInitialized = prefs.getBool(_hasInitializedFloatKey) ?? false;
      debugPrint('Float initialization check: $hasInitialized');
      return hasInitialized;
    } catch (e) {
      debugPrint('Error checking float initialization: $e');
      return false;
    }
  }

  /// Initialize float data for both telcos
  static Future<bool> initializeFloat({
    required double mtnFloat,
    required double airtelFloat,
  }) async {
    try {
      final prefs = await _instance;
      final now = DateTime.now();

      // Create initial float data
      final mtnData = FloatData(
        telco: Telco.mtn,
        currentFloat: mtnFloat,
        startingFloat: mtnFloat,
        lastUpdated: now,
      );

      final airtelData = FloatData(
        telco: Telco.airtel,
        currentFloat: airtelFloat,
        startingFloat: airtelFloat,
        lastUpdated: now,
      );

      // Save float data
      await prefs.setString(_mtnFloatKey, json.encode(mtnData.toJson()));
      await prefs.setString(_airtelFloatKey, json.encode(airtelData.toJson()));
      
      // Mark as initialized
      await prefs.setBool(_hasInitializedFloatKey, true);

      debugPrint('Initialized float - MTN: $mtnFloat, Airtel: $airtelFloat');
      return true;
    } catch (e) {
      debugPrint('Error initializing float: $e');
      return false;
    }
  }

  /// Get float data for specific telco
  static Future<FloatData?> getFloatData(Telco telco) async {
    try {
      final prefs = await _instance;
      final key = telco == Telco.mtn ? _mtnFloatKey : _airtelFloatKey;
      final jsonString = prefs.getString(key);
      
      if (jsonString != null) {
        final jsonData = json.decode(jsonString) as Map<String, dynamic>;
        final floatData = FloatData.fromJson(jsonData);
        debugPrint('Loaded ${telco.displayName} float data: ${floatData.currentFloat}');
        return floatData;
      }
    } catch (e) {
      debugPrint('Error loading ${telco.displayName} float data: $e');
    }
    return null;
  }

  /// Get float data for both telcos
  static Future<Map<Telco, FloatData>> getAllFloatData() async {
    final mtnData = await getFloatData(Telco.mtn);
    final airtelData = await getFloatData(Telco.airtel);
    
    return {
      if (mtnData != null) Telco.mtn: mtnData,
      if (airtelData != null) Telco.airtel: airtelData,
    };
  }

  /// Update float data for specific telco
  static Future<bool> updateFloatData(FloatData floatData) async {
    try {
      final prefs = await _instance;
      final key = floatData.telco == Telco.mtn ? _mtnFloatKey : _airtelFloatKey;
      
      final updatedData = floatData.copyWith(lastUpdated: DateTime.now());
      await prefs.setString(key, json.encode(updatedData.toJson()));
      
      debugPrint('Updated ${floatData.telco.displayName} float data: ${updatedData.currentFloat}');
      return true;
    } catch (e) {
      debugPrint('Error updating ${floatData.telco.displayName} float data: $e');
      return false;
    }
  }

  /// Reset daily counters (call at start of new day)
  static Future<bool> resetDailyCounters() async {
    try {
      final allData = await getAllFloatData();
      
      for (final entry in allData.entries) {
        final telco = entry.key;
        final data = entry.value;
        
        final resetData = data.copyWith(
          startingFloat: data.currentFloat,
          dailyDeposits: 0.0,
          dailyWithdrawals: 0.0,
          dailyFloatTopUps: 0.0,
          dailyCommission: 0.0,
          lastUpdated: DateTime.now(),
        );
        
        await updateFloatData(resetData);
      }
      
      debugPrint('Reset daily counters for all telcos');
      return true;
    } catch (e) {
      debugPrint('Error resetting daily counters: $e');
      return false;
    }
  }

  // Transaction Management

  /// Add a new transaction
  static Future<bool> addTransaction(TransactionEnhanced transaction) async {
    try {
      final transactions = await getTransactions();
      transactions.add(transaction);
      
      // Update float data based on transaction
      await _updateFloatFromTransaction(transaction);
      
      // Save updated transactions
      await _saveTransactions(transactions);
      
      debugPrint('Added transaction: ${transaction.id} for ${transaction.telco.displayName}');
      return true;
    } catch (e) {
      debugPrint('Error adding transaction: $e');
      return false;
    }
  }

  /// Get all transactions
  static Future<List<TransactionEnhanced>> getTransactions() async {
    try {
      final prefs = await _instance;
      final jsonString = prefs.getString(_transactionsKey);
      
      if (jsonString != null) {
        final jsonList = json.decode(jsonString) as List<dynamic>;
        return jsonList.map((json) => TransactionEnhanced.fromJson(json)).toList();
      }
    } catch (e) {
      debugPrint('Error loading transactions: $e');
    }
    return [];
  }

  /// Get transactions for specific telco
  static Future<List<TransactionEnhanced>> getTransactionsForTelco(Telco telco) async {
    final allTransactions = await getTransactions();
    return allTransactions.where((t) => t.telco == telco).toList();
  }

  /// Save transactions list
  static Future<bool> _saveTransactions(List<TransactionEnhanced> transactions) async {
    try {
      final prefs = await _instance;
      final jsonList = transactions.map((t) => t.toJson()).toList();
      await prefs.setString(_transactionsKey, json.encode(jsonList));
      return true;
    } catch (e) {
      debugPrint('Error saving transactions: $e');
      return false;
    }
  }

  /// Update float data based on transaction
  static Future<void> _updateFloatFromTransaction(TransactionEnhanced transaction) async {
    final floatData = await getFloatData(transaction.telco);
    if (floatData == null) return;

    double newCurrentFloat = floatData.currentFloat;
    double newDeposits = floatData.dailyDeposits;
    double newWithdrawals = floatData.dailyWithdrawals;
    double newFloatTopUps = floatData.dailyFloatTopUps;
    double newCommission = floatData.dailyCommission;

    switch (transaction.type) {
      case TransactionType.deposit:
        newCurrentFloat += transaction.amount;
        newDeposits += transaction.amount;
        if (transaction.commission != null) {
          newCommission += transaction.commission!;
        }
        break;
      case TransactionType.withdrawal:
        newCurrentFloat -= transaction.amount;
        newWithdrawals += transaction.amount;
        if (transaction.commission != null) {
          newCommission += transaction.commission!;
        }
        break;
      case TransactionType.floatTopUp:
        newCurrentFloat += transaction.amount;
        newFloatTopUps += transaction.amount;
        break;
      case TransactionType.commission:
        newCommission += transaction.amount;
        break;
    }

    final updatedData = floatData.copyWith(
      currentFloat: newCurrentFloat,
      dailyDeposits: newDeposits,
      dailyWithdrawals: newWithdrawals,
      dailyFloatTopUps: newFloatTopUps,
      dailyCommission: newCommission,
    );

    await updateFloatData(updatedData);
  }

  /// Clear all data (for testing)
  static Future<bool> clearAllData() async {
    try {
      final prefs = await _instance;
      await prefs.remove(_hasInitializedFloatKey);
      await prefs.remove(_mtnFloatKey);
      await prefs.remove(_airtelFloatKey);
      await prefs.remove(_transactionsKey);
      debugPrint('Cleared all float data');
      return true;
    } catch (e) {
      debugPrint('Error clearing float data: $e');
      return false;
    }
  }
}
