import 'package:flutter/foundation.dart';
import 'package:agent_kujukiza/data/models/subscription_status.dart';
import 'package:agent_kujukiza/data/services/device_id_service.dart';
import 'package:agent_kujukiza/core/storage/secure_prefs.dart';

class SubscriptionService {
  static SubscriptionStatus? _cachedSubscription;
  static DateTime? _lastCheck;
  static const Duration _checkCooldown = Duration(minutes: 5);

  /// Initialize the subscription service
  static Future<void> initialize() async {
    debugPrint('Initializing SubscriptionService...');

    try {
      // Initialize dependencies
      await DeviceIdService.initialize();
      await SecurePrefs.init();

      // Load existing subscription or create trial
      await _initializeSubscription();

      debugPrint('SubscriptionService initialized successfully');
    } catch (e) {
      debugPrint('Error initializing SubscriptionService: $e');
    }
  }

  /// Initialize subscription on first run or load existing
  static Future<void> _initializeSubscription() async {
    try {
      // Check if subscription already exists
      final existingSubscription = await SecurePrefs.loadSubscriptionStatus();

      if (existingSubscription != null) {
        debugPrint('Found existing subscription: ${existingSubscription.status}');
        _cachedSubscription = existingSubscription.calculateCurrentStatus();
        await _saveSubscription(_cachedSubscription!);
        return;
      }

      // Check if this is a fresh installation
      final isFresh = await SecurePrefs.isFirstInstall();
      final hasTrialBeenUsed = await SecurePrefs.hasTrialBeenUsed();

      if (isFresh && !hasTrialBeenUsed) {
        // Create new trial subscription
        await _createTrialSubscription();
      } else {
        // No subscription and trial already used - create expired subscription
        await _createExpiredSubscription();
      }

    } catch (e) {
      debugPrint('Error initializing subscription: $e');
      // Fallback to expired subscription
      await _createExpiredSubscription();
    }
  }

  /// Create a new trial subscription
  static Future<void> _createTrialSubscription() async {
    try {
      final deviceId = await DeviceIdService.getDeviceId();
      final trialSubscription = SubscriptionStatus.createTrial(deviceId);

      await _saveSubscription(trialSubscription);
      await SecurePrefs.markTrialAsUsed();
      await SecurePrefs.setFirstInstallDate(DateTime.now());

      _cachedSubscription = trialSubscription;
      debugPrint('Created trial subscription: 30 days free');
    } catch (e) {
      debugPrint('Error creating trial subscription: $e');
      throw e;
    }
  }

  /// Create an expired subscription (when trial is used up)
  static Future<void> _createExpiredSubscription() async {
    try {
      final deviceId = await DeviceIdService.getDeviceId();
      final now = DateTime.now();

      final expiredSubscription = SubscriptionStatus(
        startDate: now.subtract(const Duration(days: 35)),
        expiresOn: now.subtract(const Duration(days: 5)),
        status: SubscriptionStatusType.expired,
        agentDeviceId: deviceId,
        isTrialPeriod: false,
        createdAt: now,
      );

      await _saveSubscription(expiredSubscription);
      _cachedSubscription = expiredSubscription;
      debugPrint('Created expired subscription');
    } catch (e) {
      debugPrint('Error creating expired subscription: $e');
      throw e;
    }
  }

  /// Get current subscription status
  static Future<SubscriptionStatus> getCurrentSubscription() async {
    // Return cached if available and recent
    if (_cachedSubscription != null && _isCheckRecent()) {
      return _cachedSubscription!;
    }

    try {
      // Load from storage
      final stored = await SecurePrefs.loadSubscriptionStatus();

      if (stored != null) {
        // Calculate current status based on dates
        _cachedSubscription = stored.calculateCurrentStatus();

        // Save updated status if it changed
        if (_cachedSubscription!.status != stored.status) {
          await _saveSubscription(_cachedSubscription!);
        }

        _lastCheck = DateTime.now();
        await SecurePrefs.saveLastSubscriptionCheck();

        return _cachedSubscription!;
      }

      // No subscription found, initialize
      await _initializeSubscription();
      return _cachedSubscription!;

    } catch (e) {
      debugPrint('Error getting current subscription: $e');

      // Return cached if available, otherwise create expired
      if (_cachedSubscription != null) {
        return _cachedSubscription!;
      }

      await _createExpiredSubscription();
      return _cachedSubscription!;
    }
  }

  /// Check if subscription allows app usage
  static Future<bool> canUseApp() async {
    final subscription = await getCurrentSubscription();
    final canUse = subscription.canUseApp;
    debugPrint('Can use app: $canUse (status: ${subscription.status})');
    return canUse;
  }

  /// Check if subscription is active
  static Future<bool> isSubscriptionActive() async {
    final subscription = await getCurrentSubscription();
    return subscription.status.isActive;
  }

  /// Check if subscription is in grace period
  static Future<bool> isInGracePeriod() async {
    final subscription = await getCurrentSubscription();
    return subscription.status.isGrace;
  }

  /// Check if subscription is expired
  static Future<bool> isSubscriptionExpired() async {
    final subscription = await getCurrentSubscription();
    return subscription.status.isExpired;
  }

  /// Simulate a payment (for testing purposes)
  static Future<bool> simulatePayment({String? mockSmsId}) async {
    debugPrint('Simulating payment...');

    try {
      final deviceId = await DeviceIdService.getDeviceId();
      final smsId = mockSmsId ?? 'mock_payment_${DateTime.now().millisecondsSinceEpoch}';

      final paidSubscription = SubscriptionStatus.createPaid(
        deviceId: deviceId,
        smsId: smsId,
      );

      await _saveSubscription(paidSubscription);
      _cachedSubscription = paidSubscription;

      debugPrint('Payment simulation successful - 30 days added');
      return true;
    } catch (e) {
      debugPrint('Error simulating payment: $e');
      return false;
    }
  }

  /// Renew subscription with real payment data
  static Future<bool> renewSubscription({
    required String smsId,
    DateTime? paymentDate,
  }) async {
    debugPrint('Renewing subscription with SMS ID: $smsId');

    try {
      final deviceId = await DeviceIdService.getDeviceId();

      final renewedSubscription = SubscriptionStatus.createPaid(
        deviceId: deviceId,
        smsId: smsId,
        paymentDate: paymentDate,
      );

      await _saveSubscription(renewedSubscription);
      _cachedSubscription = renewedSubscription;

      debugPrint('Subscription renewed successfully');
      return true;
    } catch (e) {
      debugPrint('Error renewing subscription: $e');
      return false;
    }
  }

  /// Force expire subscription (for testing)
  static Future<bool> forceExpireSubscription() async {
    debugPrint('Force expiring subscription...');

    try {
      if (_cachedSubscription == null) {
        await getCurrentSubscription();
      }

      final expiredSubscription = _cachedSubscription!.copyWith(
        expiresOn: DateTime.now().subtract(const Duration(days: 5)),
        status: SubscriptionStatusType.expired,
      );

      await _saveSubscription(expiredSubscription);
      _cachedSubscription = expiredSubscription;

      debugPrint('Subscription force expired');
      return true;
    } catch (e) {
      debugPrint('Error force expiring subscription: $e');
      return false;
    }
  }

  /// Reset subscription (for testing)
  static Future<bool> resetSubscription() async {
    debugPrint('Resetting subscription...');

    try {
      await SecurePrefs.clearAllSubscriptionData();
      await DeviceIdService.resetDeviceId();
      _cachedSubscription = null;
      _lastCheck = null;

      debugPrint('Subscription reset complete');
      return true;
    } catch (e) {
      debugPrint('Error resetting subscription: $e');
      return false;
    }
  }

  /// Get subscription info for display
  static Future<Map<String, dynamic>> getSubscriptionInfo() async {
    final subscription = await getCurrentSubscription();
    final deviceInfo = await DeviceIdService.getDeviceInfoForDisplay();

    return {
      'status': subscription.status.displayName,
      'statusMessage': subscription.statusMessage,
      'expiresOn': subscription.expiresOn.toIso8601String(),
      'daysRemaining': subscription.daysRemaining,
      'isTrialPeriod': subscription.isTrialPeriod,
      'canUseApp': subscription.canUseApp,
      'deviceInfo': deviceInfo,
      'agentDeviceId': subscription.agentDeviceId.substring(0, 8) + '...',
    };
  }

  /// Save subscription to secure storage
  static Future<void> _saveSubscription(SubscriptionStatus subscription) async {
    await SecurePrefs.saveSubscriptionStatus(subscription);
  }

  /// Check if last check was recent (within cooldown period)
  static bool _isCheckRecent() {
    if (_lastCheck == null) return false;
    return DateTime.now().difference(_lastCheck!) < _checkCooldown;
  }

  /// Refresh subscription status (force check)
  static Future<SubscriptionStatus> refreshSubscriptionStatus() async {
    debugPrint('Refreshing subscription status...');
    _lastCheck = null; // Force refresh
    return await getCurrentSubscription();
  }

  /// Check if device has been tampered with
  static Future<bool> checkDeviceIntegrity() async {
    try {
      final subscription = await getCurrentSubscription();
      final currentDeviceId = await DeviceIdService.getDeviceId();

      if (subscription.agentDeviceId != currentDeviceId) {
        debugPrint('Device ID mismatch detected!');
        return false;
      }

      final isTampered = await DeviceIdService.isDeviceTampered();
      if (isTampered) {
        debugPrint('Device tampering detected!');
        return false;
      }

      return true;
    } catch (e) {
      debugPrint('Error checking device integrity: $e');
      return false;
    }
  }
}
