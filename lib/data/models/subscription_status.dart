import 'dart:convert';

enum SubscriptionStatusType {
  active,
  grace,
  expired;

  String get displayName {
    switch (this) {
      case SubscriptionStatusType.active:
        return 'Active';
      case SubscriptionStatusType.grace:
        return 'Grace Period';
      case SubscriptionStatusType.expired:
        return 'Expired';
    }
  }

  bool get isActive => this == SubscriptionStatusType.active;
  bool get isGrace => this == SubscriptionStatusType.grace;
  bool get isExpired => this == SubscriptionStatusType.expired;
  bool get canUseApp => this == SubscriptionStatusType.active || this == SubscriptionStatusType.grace;
}

class SubscriptionStatus {
  final DateTime startDate;
  final DateTime expiresOn;
  final SubscriptionStatusType status;
  final String? paidThroughSmsId;
  final String agentDeviceId;
  final bool isTrialPeriod;
  final DateTime createdAt;
  final DateTime? lastPaymentDate;

  const SubscriptionStatus({
    required this.startDate,
    required this.expiresOn,
    required this.status,
    required this.agentDeviceId,
    this.paidThroughSmsId,
    this.isTrialPeriod = false,
    required this.createdAt,
    this.lastPaymentDate,
  });

  /// Create a new trial subscription (30 days free)
  factory SubscriptionStatus.createTrial(String deviceId) {
    final now = DateTime.now();
    return SubscriptionStatus(
      startDate: now,
      expiresOn: now.add(const Duration(days: 30)),
      status: SubscriptionStatusType.active,
      agentDeviceId: deviceId,
      isTrialPeriod: true,
      createdAt: now,
    );
  }

  /// Create a paid subscription (30 days from payment)
  factory SubscriptionStatus.createPaid({
    required String deviceId,
    required String smsId,
    DateTime? paymentDate,
  }) {
    final now = paymentDate ?? DateTime.now();
    return SubscriptionStatus(
      startDate: now,
      expiresOn: now.add(const Duration(days: 30)),
      status: SubscriptionStatusType.active,
      agentDeviceId: deviceId,
      paidThroughSmsId: smsId,
      isTrialPeriod: false,
      createdAt: now,
      lastPaymentDate: now,
    );
  }

  /// Calculate current status based on dates
  SubscriptionStatus calculateCurrentStatus() {
    final now = DateTime.now();
    final gracePeriodEnd = expiresOn.add(const Duration(days: 3));

    SubscriptionStatusType newStatus;
    if (now.isBefore(expiresOn)) {
      newStatus = SubscriptionStatusType.active;
    } else if (now.isBefore(gracePeriodEnd)) {
      newStatus = SubscriptionStatusType.grace;
    } else {
      newStatus = SubscriptionStatusType.expired;
    }

    return copyWith(status: newStatus);
  }

  /// Get days remaining in subscription
  int get daysRemaining {
    final now = DateTime.now();
    if (now.isAfter(expiresOn)) return 0;
    return expiresOn.difference(now).inDays;
  }

  /// Get hours remaining in subscription
  int get hoursRemaining {
    final now = DateTime.now();
    if (now.isAfter(expiresOn)) return 0;
    return expiresOn.difference(now).inHours;
  }

  /// Check if subscription is in grace period
  bool get isInGracePeriod {
    final now = DateTime.now();
    final gracePeriodEnd = expiresOn.add(const Duration(days: 3));
    return now.isAfter(expiresOn) && now.isBefore(gracePeriodEnd);
  }

  /// Get grace period days remaining
  int get graceDaysRemaining {
    if (!isInGracePeriod) return 0;
    final now = DateTime.now();
    final gracePeriodEnd = expiresOn.add(const Duration(days: 3));
    return gracePeriodEnd.difference(now).inDays;
  }

  /// Check if subscription allows app usage
  bool get canUseApp => status.canUseApp;

  /// Get user-friendly status message
  String get statusMessage {
    switch (status) {
      case SubscriptionStatusType.active:
        if (isTrialPeriod) {
          return 'Free trial - $daysRemaining days remaining';
        } else {
          return 'Active - $daysRemaining days remaining';
        }
      case SubscriptionStatusType.grace:
        return 'Grace period - $graceDaysRemaining days to renew';
      case SubscriptionStatusType.expired:
        return 'Subscription expired';
    }
  }

  /// Copy with new values
  SubscriptionStatus copyWith({
    DateTime? startDate,
    DateTime? expiresOn,
    SubscriptionStatusType? status,
    String? paidThroughSmsId,
    String? agentDeviceId,
    bool? isTrialPeriod,
    DateTime? createdAt,
    DateTime? lastPaymentDate,
  }) {
    return SubscriptionStatus(
      startDate: startDate ?? this.startDate,
      expiresOn: expiresOn ?? this.expiresOn,
      status: status ?? this.status,
      paidThroughSmsId: paidThroughSmsId ?? this.paidThroughSmsId,
      agentDeviceId: agentDeviceId ?? this.agentDeviceId,
      isTrialPeriod: isTrialPeriod ?? this.isTrialPeriod,
      createdAt: createdAt ?? this.createdAt,
      lastPaymentDate: lastPaymentDate ?? this.lastPaymentDate,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'startDate': startDate.toIso8601String(),
      'expiresOn': expiresOn.toIso8601String(),
      'status': status.index,
      'paidThroughSmsId': paidThroughSmsId,
      'agentDeviceId': agentDeviceId,
      'isTrialPeriod': isTrialPeriod,
      'createdAt': createdAt.toIso8601String(),
      'lastPaymentDate': lastPaymentDate?.toIso8601String(),
    };
  }

  /// Create from JSON
  factory SubscriptionStatus.fromJson(Map<String, dynamic> json) {
    return SubscriptionStatus(
      startDate: DateTime.parse(json['startDate']),
      expiresOn: DateTime.parse(json['expiresOn']),
      status: SubscriptionStatusType.values[json['status']],
      paidThroughSmsId: json['paidThroughSmsId'],
      agentDeviceId: json['agentDeviceId'],
      isTrialPeriod: json['isTrialPeriod'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      lastPaymentDate: json['lastPaymentDate'] != null 
          ? DateTime.parse(json['lastPaymentDate'])
          : null,
    );
  }

  /// Convert to JSON string
  String toJsonString() => jsonEncode(toJson());

  /// Create from JSON string
  factory SubscriptionStatus.fromJsonString(String jsonString) {
    return SubscriptionStatus.fromJson(jsonDecode(jsonString));
  }

  @override
  String toString() {
    return 'SubscriptionStatus(status: $status, expiresOn: $expiresOn, isTrialPeriod: $isTrialPeriod, daysRemaining: $daysRemaining)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is SubscriptionStatus &&
        other.startDate == startDate &&
        other.expiresOn == expiresOn &&
        other.status == status &&
        other.paidThroughSmsId == paidThroughSmsId &&
        other.agentDeviceId == agentDeviceId &&
        other.isTrialPeriod == isTrialPeriod;
  }

  @override
  int get hashCode {
    return startDate.hashCode ^
        expiresOn.hashCode ^
        status.hashCode ^
        paidThroughSmsId.hashCode ^
        agentDeviceId.hashCode ^
        isTrialPeriod.hashCode;
  }
}
