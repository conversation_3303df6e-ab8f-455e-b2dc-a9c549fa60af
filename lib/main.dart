import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/domain/localization/locale_provider.dart';
import 'package:agent_kujukiza/domain/providers/subscription_provider.dart';
import 'package:agent_kujukiza/presentation/onboarding/logic/onboarding_controller.dart';
import 'package:agent_kujukiza/core/storage/local_prefs.dart';
import 'package:agent_kujukiza/data/services/permission_service.dart';
import 'package:agent_kujukiza/data/services/subscription_service.dart';
import 'package:agent_kujukiza/data/services/device_id_service.dart';
import 'package:agent_kujukiza/core/storage/secure_prefs.dart';
import 'package:agent_kujukiza/domain/localization/fallback_material_localizations.dart';
import 'package:agent_kujukiza/routing/app_router.dart';

void main() async {
  // Ensure Flutter is initialized
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize core services
  await LocalPrefs.init();
  await SecurePrefs.init();
  await DeviceIdService.initialize();
  await PermissionService.initialize();
  await SubscriptionService.initialize();

  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (context) => LocaleProvider()),
        ChangeNotifierProvider(create: (context) => SubscriptionProvider()),
        ChangeNotifierProvider(create: (context) => OnboardingController()),
      ],
      child: const AgentKujukizaApp(),
    ),
  );
}

class AgentKujukizaApp extends StatelessWidget {
  const AgentKujukizaApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<LocaleProvider>(
      builder: (context, localeProvider, _) {
        return MaterialApp.router(
          debugShowCheckedModeBanner: false,
          title: 'Agent Kujukiza',
          theme: AppTheme.lightTheme,
          locale: localeProvider.locale,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            FallbackMaterialLocalizationsDelegate(),
            FallbackCupertinoLocalizationsDelegate(),
            GlobalWidgetsLocalizations.delegate,
          ],
          supportedLocales: LocaleProvider.supportedLocales,
          routerConfig: AppRouter.router,
          builder: (context, child) {
            // Ensure text scales properly for accessibility
            return MediaQuery(
              data: MediaQuery.of(context).copyWith(
                textScaler: const TextScaler.linear(1.0),
              ),
              child: child!,
            );
          },
        );
      },
    );
  }
}
