import 'package:intl/intl.dart';

class MoneyFormatter {
  static final NumberFormat _ugxFormatter = NumberFormat('#,##0.00', 'en_US');
  static final NumberFormat _ugxFormatterNoDecimals = NumberFormat('#,##0', 'en_US');

  /// Formats money amount with proper commas and decimals
  /// Example: 1234567.89 -> "UGX 1,234,567.89"
  static String formatUGX(double amount, {bool showDecimals = true}) {
    if (showDecimals || amount % 1 != 0) {
      return 'UGX ${_ugxFormatter.format(amount)}';
    } else {
      return 'UGX ${_ugxFormatterNoDecimals.format(amount)}';
    }
  }

  /// Formats money amount without currency symbol
  /// Example: 1234567.89 -> "1,234,567.89"
  static String formatAmount(double amount, {bool showDecimals = true}) {
    if (showDecimals || amount % 1 != 0) {
      return _ugxFormatter.format(amount);
    } else {
      return _ugxFormatterNoDecimals.format(amount);
    }
  }

  /// Formats money with sign prefix for transactions
  /// Example: 1234.56 -> "+1,234.56" or "-1,234.56"
  static String formatAmountWithSign(double amount, {bool isPositive = true, bool showDecimals = true}) {
    final prefix = isPositive ? '+' : '-';
    final absAmount = amount.abs();

    if (showDecimals || absAmount % 1 != 0) {
      return '$prefix${_ugxFormatter.format(absAmount)}';
    } else {
      return '$prefix${_ugxFormatterNoDecimals.format(absAmount)}';
    }
  }

  /// Checks if amount should show decimals (non-zero decimal part)
  static bool shouldShowDecimals(double amount) {
    return amount % 1 != 0;
  }

  /// Adds thousands separators to a numeric string
  /// Example: "1234567" -> "1,234,567"
  static String addThousandsSeparators(String numericString) {
    if (numericString.isEmpty) return numericString;

    // Remove any existing commas
    final cleanString = numericString.replaceAll(',', '');

    // Parse as number and format
    final number = int.tryParse(cleanString);
    if (number == null) return numericString;

    return _ugxFormatterNoDecimals.format(number);
  }
}
