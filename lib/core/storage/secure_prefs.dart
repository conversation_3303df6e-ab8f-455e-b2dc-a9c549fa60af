import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:agent_kujukiza/data/models/subscription_status.dart';

class SecurePrefs {
  static const String _subscriptionKey = 'subscription_status';
  static const String _trialUsedKey = 'trial_used';
  static const String _firstInstallDateKey = 'first_install_date';
  static const String _lastSubscriptionCheckKey = 'last_subscription_check';
  
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_PKCS1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  /// Initialize secure preferences
  static Future<void> init() async {
    debugPrint('Initializing SecurePrefs...');
    // Test if secure storage is working
    try {
      await _secureStorage.write(key: 'test', value: 'test');
      await _secureStorage.delete(key: 'test');
      debugPrint('SecurePrefs initialized successfully');
    } catch (e) {
      debugPrint('SecurePrefs initialization error: $e');
    }
  }

  // Subscription Management

  /// Save subscription status
  static Future<bool> saveSubscriptionStatus(SubscriptionStatus subscription) async {
    try {
      final jsonString = subscription.toJsonString();
      await _secureStorage.write(key: _subscriptionKey, value: jsonString);
      debugPrint('Saved subscription status: ${subscription.status}');
      return true;
    } catch (e) {
      debugPrint('Error saving subscription status: $e');
      return false;
    }
  }

  /// Load subscription status
  static Future<SubscriptionStatus?> loadSubscriptionStatus() async {
    try {
      final jsonString = await _secureStorage.read(key: _subscriptionKey);
      if (jsonString != null && jsonString.isNotEmpty) {
        final subscription = SubscriptionStatus.fromJsonString(jsonString);
        debugPrint('Loaded subscription status: ${subscription.status}');
        return subscription;
      }
    } catch (e) {
      debugPrint('Error loading subscription status: $e');
    }
    
    debugPrint('No subscription status found');
    return null;
  }

  /// Delete subscription status
  static Future<bool> deleteSubscriptionStatus() async {
    try {
      await _secureStorage.delete(key: _subscriptionKey);
      debugPrint('Deleted subscription status');
      return true;
    } catch (e) {
      debugPrint('Error deleting subscription status: $e');
      return false;
    }
  }

  // Trial Management

  /// Check if trial has been used
  static Future<bool> hasTrialBeenUsed() async {
    try {
      final value = await _secureStorage.read(key: _trialUsedKey);
      final hasUsed = value == 'true';
      debugPrint('Trial used check: $hasUsed');
      return hasUsed;
    } catch (e) {
      debugPrint('Error checking trial usage: $e');
      return false;
    }
  }

  /// Mark trial as used
  static Future<bool> markTrialAsUsed() async {
    try {
      await _secureStorage.write(key: _trialUsedKey, value: 'true');
      debugPrint('Marked trial as used');
      return true;
    } catch (e) {
      debugPrint('Error marking trial as used: $e');
      return false;
    }
  }

  /// Reset trial status (for testing)
  static Future<bool> resetTrialStatus() async {
    try {
      await _secureStorage.delete(key: _trialUsedKey);
      debugPrint('Reset trial status');
      return true;
    } catch (e) {
      debugPrint('Error resetting trial status: $e');
      return false;
    }
  }

  // Installation Tracking

  /// Get first install date
  static Future<DateTime?> getFirstInstallDate() async {
    try {
      final dateString = await _secureStorage.read(key: _firstInstallDateKey);
      if (dateString != null) {
        final date = DateTime.parse(dateString);
        debugPrint('First install date: $date');
        return date;
      }
    } catch (e) {
      debugPrint('Error getting first install date: $e');
    }
    
    debugPrint('No first install date found');
    return null;
  }

  /// Set first install date
  static Future<bool> setFirstInstallDate(DateTime date) async {
    try {
      await _secureStorage.write(key: _firstInstallDateKey, value: date.toIso8601String());
      debugPrint('Set first install date: $date');
      return true;
    } catch (e) {
      debugPrint('Error setting first install date: $e');
      return false;
    }
  }

  /// Check if this is first install
  static Future<bool> isFirstInstall() async {
    final firstInstallDate = await getFirstInstallDate();
    final isFirst = firstInstallDate == null;
    debugPrint('Is first install: $isFirst');
    return isFirst;
  }

  // Subscription Check Tracking

  /// Save last subscription check timestamp
  static Future<bool> saveLastSubscriptionCheck() async {
    try {
      final timestamp = DateTime.now().toIso8601String();
      await _secureStorage.write(key: _lastSubscriptionCheckKey, value: timestamp);
      debugPrint('Saved last subscription check: $timestamp');
      return true;
    } catch (e) {
      debugPrint('Error saving last subscription check: $e');
      return false;
    }
  }

  /// Load last subscription check timestamp
  static Future<DateTime?> loadLastSubscriptionCheck() async {
    try {
      final timestampStr = await _secureStorage.read(key: _lastSubscriptionCheckKey);
      if (timestampStr != null) {
        final timestamp = DateTime.parse(timestampStr);
        debugPrint('Loaded last subscription check: $timestamp');
        return timestamp;
      }
    } catch (e) {
      debugPrint('Error loading last subscription check: $e');
    }
    
    return null;
  }

  // Utility Methods

  /// Get all stored keys (for debugging)
  static Future<Set<String>> getAllKeys() async {
    try {
      final allData = await _secureStorage.readAll();
      return allData.keys.toSet();
    } catch (e) {
      debugPrint('Error getting all keys: $e');
      return <String>{};
    }
  }

  /// Clear all subscription-related data (for testing)
  static Future<bool> clearAllSubscriptionData() async {
    try {
      await _secureStorage.delete(key: _subscriptionKey);
      await _secureStorage.delete(key: _trialUsedKey);
      await _secureStorage.delete(key: _firstInstallDateKey);
      await _secureStorage.delete(key: _lastSubscriptionCheckKey);
      debugPrint('Cleared all subscription data');
      return true;
    } catch (e) {
      debugPrint('Error clearing subscription data: $e');
      return false;
    }
  }

  /// Clear all stored data (for testing/reset)
  static Future<bool> clearAll() async {
    try {
      await _secureStorage.deleteAll();
      debugPrint('Cleared all secure storage');
      return true;
    } catch (e) {
      debugPrint('Error clearing all secure storage: $e');
      return false;
    }
  }

  /// Export all data for debugging
  static Future<Map<String, String>> exportAllData() async {
    try {
      final allData = await _secureStorage.readAll();
      debugPrint('Exported ${allData.length} secure storage entries');
      return allData;
    } catch (e) {
      debugPrint('Error exporting data: $e');
      return {};
    }
  }

  /// Check storage health
  static Future<bool> checkStorageHealth() async {
    try {
      const testKey = 'health_check';
      const testValue = 'test_value';
      
      // Write test data
      await _secureStorage.write(key: testKey, value: testValue);
      
      // Read test data
      final readValue = await _secureStorage.read(key: testKey);
      
      // Clean up
      await _secureStorage.delete(key: testKey);
      
      final isHealthy = readValue == testValue;
      debugPrint('Storage health check: ${isHealthy ? "PASS" : "FAIL"}');
      return isHealthy;
    } catch (e) {
      debugPrint('Storage health check failed: $e');
      return false;
    }
  }
}
