import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import 'package:agent_kujukiza/domain/models/onboarding_state.dart';

class LocalPrefs {
  static const String _onboardingStateKey = 'onboarding_state';
  static const String _isOnboardingCompleteKey = 'is_onboarding_complete';
  static const String _selectedLanguageKey = 'selected_language';
  static const String _lastPermissionCheckKey = 'last_permission_check';
  
  static SharedPreferences? _prefs;

  /// Initialize SharedPreferences instance
  static Future<void> init() async {
    _prefs ??= await SharedPreferences.getInstance();
  }

  /// Ensure preferences are initialized
  static Future<SharedPreferences> get _instance async {
    if (_prefs == null) {
      await init();
    }
    return _prefs!;
  }

  // Onboarding State Management
  
  /// Save complete onboarding state
  static Future<bool> saveOnboardingState(OnboardingState state) async {
    try {
      final prefs = await _instance;
      final stateJson = json.encode(state.toJson());
      
      final success = await prefs.setString(_onboardingStateKey, stateJson);
      
      // Also save the completion flag separately for quick access
      await prefs.setBool(_isOnboardingCompleteKey, state.isOnboardingComplete);
      
      debugPrint('Saved onboarding state: $state');
      return success;
    } catch (e) {
      debugPrint('Error saving onboarding state: $e');
      return false;
    }
  }

  /// Load onboarding state
  static Future<OnboardingState> loadOnboardingState() async {
    try {
      final prefs = await _instance;
      final stateJson = prefs.getString(_onboardingStateKey);
      
      if (stateJson != null) {
        final stateMap = json.decode(stateJson) as Map<String, dynamic>;
        final state = OnboardingState.fromJson(stateMap);
        debugPrint('Loaded onboarding state: $state');
        return state;
      }
    } catch (e) {
      debugPrint('Error loading onboarding state: $e');
    }
    
    // Return default state if no saved state or error
    debugPrint('Returning default onboarding state');
    return const OnboardingState();
  }

  /// Quick check if onboarding is complete
  static Future<bool> isOnboardingComplete() async {
    try {
      final prefs = await _instance;
      final isComplete = prefs.getBool(_isOnboardingCompleteKey) ?? false;
      debugPrint('Onboarding complete check: $isComplete');
      return isComplete;
    } catch (e) {
      debugPrint('Error checking onboarding completion: $e');
      return false;
    }
  }

  /// Mark onboarding as complete
  static Future<bool> markOnboardingComplete() async {
    try {
      final prefs = await _instance;
      final success = await prefs.setBool(_isOnboardingCompleteKey, true);
      debugPrint('Marked onboarding as complete: $success');
      return success;
    } catch (e) {
      debugPrint('Error marking onboarding complete: $e');
      return false;
    }
  }

  /// Reset onboarding (for testing/debugging)
  static Future<bool> resetOnboarding() async {
    try {
      final prefs = await _instance;
      await prefs.remove(_onboardingStateKey);
      await prefs.remove(_isOnboardingCompleteKey);
      debugPrint('Reset onboarding state');
      return true;
    } catch (e) {
      debugPrint('Error resetting onboarding: $e');
      return false;
    }
  }

  // Language Preferences

  /// Save selected language
  static Future<bool> saveSelectedLanguage(String languageCode) async {
    try {
      final prefs = await _instance;
      final success = await prefs.setString(_selectedLanguageKey, languageCode);
      debugPrint('Saved language: $languageCode');
      return success;
    } catch (e) {
      debugPrint('Error saving language: $e');
      return false;
    }
  }

  /// Load selected language
  static Future<String?> loadSelectedLanguage() async {
    try {
      final prefs = await _instance;
      final language = prefs.getString(_selectedLanguageKey);
      debugPrint('Loaded language: $language');
      return language;
    } catch (e) {
      debugPrint('Error loading language: $e');
      return null;
    }
  }

  // Permission Tracking

  /// Save last permission check timestamp
  static Future<bool> saveLastPermissionCheck() async {
    try {
      final prefs = await _instance;
      final timestamp = DateTime.now().toIso8601String();
      final success = await prefs.setString(_lastPermissionCheckKey, timestamp);
      debugPrint('Saved last permission check: $timestamp');
      return success;
    } catch (e) {
      debugPrint('Error saving permission check timestamp: $e');
      return false;
    }
  }

  /// Load last permission check timestamp
  static Future<DateTime?> loadLastPermissionCheck() async {
    try {
      final prefs = await _instance;
      final timestampStr = prefs.getString(_lastPermissionCheckKey);
      
      if (timestampStr != null) {
        final timestamp = DateTime.parse(timestampStr);
        debugPrint('Loaded last permission check: $timestamp');
        return timestamp;
      }
    } catch (e) {
      debugPrint('Error loading permission check timestamp: $e');
    }
    return null;
  }

  // Utility Methods

  /// Clear all stored data (for testing/reset)
  static Future<bool> clearAll() async {
    try {
      final prefs = await _instance;
      await prefs.clear();
      debugPrint('Cleared all local preferences');
      return true;
    } catch (e) {
      debugPrint('Error clearing preferences: $e');
      return false;
    }
  }

  /// Get all stored keys (for debugging)
  static Future<Set<String>> getAllKeys() async {
    try {
      final prefs = await _instance;
      return prefs.getKeys();
    } catch (e) {
      debugPrint('Error getting all keys: $e');
      return <String>{};
    }
  }
}
