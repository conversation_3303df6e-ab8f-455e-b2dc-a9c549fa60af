import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/domain/providers/subscription_provider.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/bottom_navigation_bar.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _isHelpExpanded = false;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          l10n.settings,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        centerTitle: true,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: Consumer<SubscriptionProvider>(
        builder: (context, subscriptionProvider, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(AppTheme.paddingMedium),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Subscription Status Card
                _buildSubscriptionCard(context, subscriptionProvider),

                const SizedBox(height: AppTheme.paddingLarge),

                // App Information Section
                _buildSectionCard(
                  context,
                  title: l10n.appInformation,
                  children: [
                    _buildInfoRow(
                      context,
                      icon: Icons.info_outline,
                      label: l10n.version,
                      value: '1.0.0',
                    ),
                    const SizedBox(height: AppTheme.paddingSmall),
                    _buildInfoRow(
                      context,
                      icon: Icons.phone_android,
                      label: l10n.platform,
                      value: 'Android',
                    ),
                    const SizedBox(height: AppTheme.paddingSmall),
                    _buildInfoRow(
                      context,
                      icon: Icons.storage,
                      label: l10n.dataStorage,
                      value: l10n.localDevice,
                    ),
                  ],
                ),

                const SizedBox(height: AppTheme.paddingLarge),

                // Help Section
                _buildHelpSection(context),

                const SizedBox(height: AppTheme.paddingLarge),

                // Debug Section (for testing)
                if (true) // Set to false in production
                  _buildDebugSection(context, subscriptionProvider),

                // Bottom padding for navigation bar
                const SizedBox(height: 100),
              ],
            ),
          );
        },
      ),
      bottomNavigationBar: const AppBottomNavigationBar(
        currentIndex: 2, // Settings tab is active (now index 2 after removing export)
      ),
    );
  }

  Widget _buildSubscriptionCard(BuildContext context, SubscriptionProvider provider) {
    final l10n = context.l10n;
    final theme = Theme.of(context);
    final subscription = provider.subscription;

    // Handle null subscription
    if (subscription == null) {
      return Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppTheme.paddingLarge),
        decoration: BoxDecoration(
          color: Colors.grey.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.grey,
            width: 2,
          ),
        ),
        child: Column(
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Loading subscription status...',
              style: theme.textTheme.bodyMedium,
            ),
          ],
        ),
      );
    }

    final isActive = subscription.status.canUseApp;
    final statusColor = isActive ? Colors.green : Colors.red;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.paddingLarge),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: statusColor,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isActive ? Icons.check_circle : Icons.error,
                color: statusColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '${l10n.subscription}: ${subscription.status.displayName}',
                  style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: statusColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.paddingMedium),

          if (isActive) ...[
            Text(
              '${subscription.daysRemaining} ${l10n.daysRemaining}',
              style: theme.textTheme.bodyLarge?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
            ),
            Text(
              'Expires: ${provider.getFormattedExpiryDate()}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ] else ...[
            Text(
              l10n.subscriptionExpiredMessage,
              style: theme.textTheme.bodyLarge?.copyWith(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              l10n.renewSubscriptionMessage,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
          ],

          const SizedBox(height: AppTheme.paddingMedium),
          Text(
            l10n.monthlyFee,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),

          const SizedBox(height: AppTheme.paddingLarge),

          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: provider.isLoading ? null : () => _showRenewalDialog(context, provider),
              style: ElevatedButton.styleFrom(
                backgroundColor: isActive ? theme.colorScheme.primary : Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: provider.isLoading
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Text(
                      l10n.renewNow,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(BuildContext context, {required String title, required List<Widget> children}) {
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.paddingLarge),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          ...children,
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, {required IconData icon, required String label, required String value}) {
    final theme = Theme.of(context);

    return Row(
      children: [
        Icon(
          icon,
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ),
        Text(
          value,
          style: theme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  Widget _buildHelpSection(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.shadow.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          InkWell(
            onTap: () {
              setState(() {
                _isHelpExpanded = !_isHelpExpanded;
              });
            },
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: const EdgeInsets.all(AppTheme.paddingLarge),
              child: Row(
                children: [
                  Icon(
                    Icons.help_outline,
                    color: theme.colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      l10n.howToUse,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.onSurface,
                      ),
                    ),
                  ),
                  Icon(
                    _isHelpExpanded ? Icons.expand_less : Icons.expand_more,
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ],
              ),
            ),
          ),
          if (_isHelpExpanded) ...[
            const Divider(height: 1),
            Padding(
              padding: const EdgeInsets.all(AppTheme.paddingLarge),
              child: Column(
                children: [
                  _buildHelpStep(context, '1', l10n.step1, l10n.step1Description, Icons.sms),
                  const SizedBox(height: AppTheme.paddingMedium),
                  _buildHelpStep(context, '2', l10n.step2, l10n.step2Description, Icons.account_balance_wallet),
                  const SizedBox(height: AppTheme.paddingMedium),
                  _buildHelpStep(context, '3', l10n.step3, l10n.step3Description, Icons.list_alt),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildHelpStep(BuildContext context, String stepNumber, String title, String description, IconData icon) {
    final theme = Theme.of(context);

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Center(
            child: Text(
              stepNumber,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    icon,
                    size: 18,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 6),
                  Text(
                    title,
                    style: theme.textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: theme.colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDebugSection(BuildContext context, SubscriptionProvider provider) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.paddingLarge),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.orange,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.debugSection,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: () async {
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    await provider.expireSubscription();
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        const SnackBar(content: Text('Subscription expired for testing')),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(l10n.expireSubscription),
                ),
              ),
              const SizedBox(width: AppTheme.paddingSmall),
              Expanded(
                child: ElevatedButton(
                  onPressed: () async {
                    final scaffoldMessenger = ScaffoldMessenger.of(context);
                    final success = await provider.simulatePayment();
                    if (mounted) {
                      scaffoldMessenger.showSnackBar(
                        SnackBar(
                          content: Text(success ? 'Subscription renewed!' : 'Renewal failed'),
                        ),
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                  ),
                  child: Text(l10n.renewSubscription),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _showRenewalDialog(BuildContext context, SubscriptionProvider provider) {
    final l10n = context.l10n;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(l10n.renewSubscription),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(l10n.selectPaymentMethod),
            const SizedBox(height: 16),
            ...PaymentMethod.values.map((method) => ListTile(
              leading: Icon(_getPaymentMethodIcon(method)),
              title: Text(method.displayName),
              onTap: () async {
                Navigator.of(context).pop();
                await _processPayment(context, provider, method);
              },
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.cancel),
          ),
        ],
      ),
    );
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.mtnMobileMoney:
      case PaymentMethod.airtelMoney:
        return Icons.phone_android;
      case PaymentMethod.cashVoucher:
        return Icons.receipt;
      case PaymentMethod.ussd:
        return Icons.dialpad;
    }
  }

  Future<void> _processPayment(BuildContext context, SubscriptionProvider provider, PaymentMethod method) async {
    final l10n = context.l10n;
    final navigator = Navigator.of(context);

    // Show processing dialog
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(l10n.processingPayment),
          ],
        ),
      ),
    );

    final success = await provider.renewSubscription(paymentMethod: method);

    if (mounted) {
      navigator.pop(); // Close processing dialog

      // Show result dialog
      if (mounted && context.mounted) {
        showDialog(
          context: context,
          builder: (dialogContext) => AlertDialog(
            title: Text(success ? l10n.paymentSuccessful : l10n.paymentFailed),
            content: Text(
              success
                  ? '${l10n.subscriptionRenewed} ${provider.getFormattedExpiryDate()}'
                  : l10n.paymentFailed,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(dialogContext).pop(),
                child: Text(success ? l10n.ok : l10n.tryAgain),
              ),
            ],
          ),
        );
      }
    }
  }
}