import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/recent_transactions_list.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/bottom_navigation_bar.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/core/utils/money_formatter.dart';
import 'package:agent_kujukiza/routing/app_router.dart';

class TransactionsScreen extends StatefulWidget {
  const TransactionsScreen({super.key});

  @override
  State<TransactionsScreen> createState() => _TransactionsScreenState();
}

class _TransactionsScreenState extends State<TransactionsScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchVisible = false;
  Set<TransactionType> _activeFilters = {};
  List<Transaction> _allTransactions = [];
  List<Transaction> _filteredTransactions = [];

  @override
  void initState() {
    super.initState();
    _loadMockTransactions();
    _filteredTransactions = _allTransactions;
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadMockTransactions() {
    _allTransactions = [
      Transaction(
        id: 'TXN001',
        type: TransactionType.deposit,
        amount: 125000.0,
        newBalance: 456789.50,
        counterparty: 'From John Doe',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        rawSms: 'You have received UGX 125,000.00 from John Doe. New balance: UGX 456,789.50. Ref: TXN001',
      ),
      Transaction(
        id: 'TXN002',
        type: TransactionType.withdrawal,
        amount: 75000.0,
        newBalance: 331789.50,
        counterparty: 'To Mary Smith',
        timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        rawSms: 'You have sent UGX 75,000.00 to Mary Smith. New balance: UGX 331,789.50. Ref: TXN002',
      ),
      Transaction(
        id: 'TXN003',
        type: TransactionType.floatTopUp,
        amount: 200000.0,
        newBalance: 406789.50,
        counterparty: 'Float Purchase',
        timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        rawSms: 'Float purchase successful. UGX 200,000.00 added. New balance: UGX 406,789.50. Ref: TXN003',
      ),
      Transaction(
        id: 'TXN004',
        type: TransactionType.deposit,
        amount: 50000.0,
        newBalance: 381789.50,
        counterparty: 'From Alice Johnson',
        timestamp: DateTime.now().subtract(const Duration(hours: 8)),
        rawSms: 'You have received UGX 50,000.00 from Alice Johnson. New balance: UGX 381,789.50. Ref: TXN004',
      ),
      Transaction(
        id: 'TXN005',
        type: TransactionType.withdrawal,
        amount: 30000.0,
        newBalance: 351789.50,
        counterparty: 'To Bob Wilson',
        timestamp: DateTime.now().subtract(const Duration(hours: 12)),
        rawSms: 'You have sent UGX 30,000.00 to Bob Wilson. New balance: UGX 351,789.50. Ref: TXN005',
      ),
    ];
  }

  void _toggleSearch() {
    setState(() {
      _isSearchVisible = !_isSearchVisible;
      if (!_isSearchVisible) {
        _searchController.clear();
        _applyFilters();
      }
    });
  }

  void _showFilterModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildFilterModal(),
    );
  }

  void _applyFilters() {
    final searchTerm = _searchController.text.toLowerCase();

    setState(() {
      _filteredTransactions = _allTransactions.where((transaction) {
        // Apply type filters
        if (_activeFilters.isNotEmpty && !_activeFilters.contains(transaction.type)) {
          return false;
        }

        // Apply search filter
        if (searchTerm.isNotEmpty) {
          final matchesCounterparty = transaction.counterparty.toLowerCase().contains(searchTerm);
          final matchesAmount = transaction.amount.toString().contains(searchTerm);
          final matchesId = transaction.id.toLowerCase().contains(searchTerm);
          final matchesSms = transaction.rawSms?.toLowerCase().contains(searchTerm) ?? false;

          return matchesCounterparty || matchesAmount || matchesId || matchesSms;
        }

        return true;
      }).toList();
    });
  }

  void _clearAllFilters() {
    setState(() {
      _activeFilters.clear();
      _searchController.clear();
      _applyFilters();
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        title: Text(
          l10n.transactions,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        leading: IconButton(
          onPressed: () {
            // Navigate back to dashboard instead of popping
            context.go(AppRoutes.dashboard);
          },
          icon: Icon(
            Icons.arrow_back,
            color: theme.colorScheme.onSurface,
          ),
        ),
        actions: [
          // Search button
          IconButton(
            onPressed: _toggleSearch,
            icon: Icon(
              Icons.search,
              color: theme.colorScheme.onSurface,
            ),
            tooltip: l10n.search,
          ),
          // Filter button
          IconButton(
            onPressed: _showFilterModal,
            icon: Icon(
              Icons.filter_list,
              color: theme.colorScheme.onSurface,
            ),
            tooltip: l10n.filter,
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar (conditionally visible)
          if (_isSearchVisible)
            _buildSearchBar(l10n),

          // Active filters display
          if (_activeFilters.isNotEmpty || _searchController.text.isNotEmpty)
            _buildActiveFilters(l10n),

          // Transaction list
          Expanded(
            child: _filteredTransactions.isEmpty
                ? _buildEmptyState(l10n, theme)
                : ListView.separated(
                    padding: const EdgeInsets.all(AppTheme.paddingMedium),
                    itemCount: _filteredTransactions.length,
                    separatorBuilder: (context, index) => const SizedBox(height: AppTheme.paddingSmall),
                    itemBuilder: (context, index) {
                      final transaction = _filteredTransactions[index];
                      return _buildTransactionItem(transaction, theme);
                    },
                  ),
          ),
        ],
      ),
      bottomNavigationBar: const AppBottomNavigationBar(
        currentIndex: 1, // Transactions tab is active
      ),
    );
  }

  Widget _buildSearchBar(AppLocalizations l10n) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      child: TextField(
        controller: _searchController,
        onChanged: (_) => _applyFilters(),
        decoration: InputDecoration(
          hintText: l10n.searchByNameOrAmount,
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _searchController.clear();
                    _applyFilters();
                  },
                  icon: const Icon(Icons.clear),
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          filled: true,
          fillColor: theme.colorScheme.surface,
        ),
      ),
    );
  }

  Widget _buildActiveFilters(AppLocalizations l10n) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.paddingMedium),
      child: Row(
        children: [
          Expanded(
            child: Wrap(
              spacing: 8,
              children: [
                ..._activeFilters.map((filter) => _buildFilterChip(filter, l10n)),
                if (_searchController.text.isNotEmpty)
                  _buildSearchChip(_searchController.text),
              ],
            ),
          ),
          TextButton(
            onPressed: _clearAllFilters,
            child: Text(l10n.clearAll),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(TransactionType filter, AppLocalizations l10n) {
    String label;
    Color color;

    switch (filter) {
      case TransactionType.deposit:
        label = l10n.deposits;
        color = Colors.green;
        break;
      case TransactionType.withdrawal:
        label = l10n.withdrawals;
        color = Colors.red;
        break;
      case TransactionType.floatTopUp:
        label = l10n.floatTopUp;
        color = Colors.blue;
        break;
    }

    return Chip(
      label: Text(label),
      backgroundColor: color.withValues(alpha: 0.1),
      side: BorderSide(color: color),
      onDeleted: () {
        setState(() {
          _activeFilters.remove(filter);
          _applyFilters();
        });
      },
    );
  }

  Widget _buildSearchChip(String searchTerm) {
    return Chip(
      label: Text('Search: "$searchTerm"'),
      backgroundColor: Colors.orange.withValues(alpha: 0.1),
      side: const BorderSide(color: Colors.orange),
      onDeleted: () {
        _searchController.clear();
        _applyFilters();
      },
    );
  }

  Widget _buildEmptyState(AppLocalizations l10n, ThemeData theme) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_wallet_outlined,
            size: 64,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          Text(
            _searchController.text.isNotEmpty || _activeFilters.isNotEmpty
                ? l10n.noMatchesFound
                : l10n.noTransactionsYet,
            style: theme.textTheme.titleMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          ElevatedButton.icon(
            onPressed: () {
              // Refresh transactions
              _loadMockTransactions();
              _applyFilters();
            },
            icon: const Icon(Icons.refresh),
            label: Text(l10n.refresh),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(Transaction transaction, ThemeData theme) {
    final timeFormat = '${transaction.timestamp.hour.toString().padLeft(2, '0')}:${transaction.timestamp.minute.toString().padLeft(2, '0')}';
    final isPositive = transaction.type == TransactionType.deposit || transaction.type == TransactionType.floatTopUp;
    final amountColor = isPositive ? Colors.green : Colors.red;

    return Card(
      elevation: 2,
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(
          horizontal: AppTheme.paddingMedium,
          vertical: 8,
        ),
        leading: _buildTransactionIcon(transaction.type),
        title: Text(
          transaction.counterparty,
          style: theme.textTheme.bodyLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        subtitle: Text(
          '$timeFormat, ${_formatDate(transaction.timestamp)}',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        trailing: SizedBox(
          width: 80,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                MoneyFormatter.formatAmountWithSign(
                  transaction.amount,
                  isPositive: isPositive,
                  showDecimals: false
                ),
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: amountColor,
                ),
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              if (transaction.newBalance != null)
                Text(
                  'Bal: ${MoneyFormatter.formatAmount(transaction.newBalance!, showDecimals: false)}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                    fontSize: 10,
                  ),
                  overflow: TextOverflow.ellipsis,
                )
              else
                Text(
                  'Bal: –––',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    fontSize: 10,
                  ),
                ),
            ],
          ),
        ),
        onTap: () => _showTransactionDetails(transaction),
      ),
    );
  }

  Widget _buildTransactionIcon(TransactionType type) {
    Color backgroundColor;
    Color iconColor;
    IconData iconData;

    switch (type) {
      case TransactionType.deposit:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        iconColor = Colors.green;
        iconData = Icons.arrow_upward;
        break;
      case TransactionType.withdrawal:
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        iconColor = Colors.red;
        iconData = Icons.arrow_downward;
        break;
      case TransactionType.floatTopUp:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        iconColor = Colors.blue;
        iconData = Icons.account_balance_wallet;
        break;
    }

    return CircleAvatar(
      backgroundColor: backgroundColor,
      child: Icon(
        iconData,
        color: iconColor,
        size: 20,
      ),
    );
  }

  String _formatDate(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (dateOnly == today) {
      return 'Today';
    } else if (dateOnly == yesterday) {
      return 'Yesterday';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }

  void _showTransactionDetails(Transaction transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Transaction Details'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (transaction.rawSms != null) ...[
              const Text('📄 Full SMS:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text(transaction.rawSms!),
              const SizedBox(height: 16),
            ],
            const Text('↳ Transaction ID:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text(transaction.id),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterModal() {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      padding: const EdgeInsets.all(AppTheme.paddingLarge),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: AppTheme.paddingMedium),

          // Title
          Text(
            l10n.filter,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppTheme.paddingLarge),

          // Filter options
          _buildFilterOption(TransactionType.deposit, l10n.deposits, Colors.green, l10n),
          _buildFilterOption(TransactionType.withdrawal, l10n.withdrawals, Colors.red, l10n),
          _buildFilterOption(TransactionType.floatTopUp, l10n.floatTopUp, Colors.blue, l10n),

          const SizedBox(height: AppTheme.paddingLarge),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () {
                    setState(() {
                      _activeFilters.clear();
                      _applyFilters();
                    });
                    Navigator.of(context).pop();
                  },
                  child: Text(l10n.clearAll),
                ),
              ),
              const SizedBox(width: AppTheme.paddingMedium),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                  child: Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFilterOption(TransactionType type, String label, Color color, AppLocalizations l10n) {
    final isSelected = _activeFilters.contains(type);

    return CheckboxListTile(
      value: isSelected,
      onChanged: (value) {
        setState(() {
          if (value == true) {
            _activeFilters.add(type);
          } else {
            _activeFilters.remove(type);
          }
          _applyFilters();
        });
      },
      title: Row(
        children: [
          CircleAvatar(
            radius: 12,
            backgroundColor: color.withValues(alpha: 0.1),
            child: Icon(
              _getIconForType(type),
              color: color,
              size: 16,
            ),
          ),
          const SizedBox(width: 12),
          Text(label),
        ],
      ),
      controlAffinity: ListTileControlAffinity.trailing,
    );
  }

  IconData _getIconForType(TransactionType type) {
    switch (type) {
      case TransactionType.deposit:
        return Icons.arrow_upward;
      case TransactionType.withdrawal:
        return Icons.arrow_downward;
      case TransactionType.floatTopUp:
        return Icons.account_balance_wallet;
    }
  }
}
