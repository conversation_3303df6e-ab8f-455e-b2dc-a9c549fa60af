import 'package:flutter/material.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/core/utils/money_formatter.dart';
import 'package:agent_kujukiza/core/enums/time_period.dart';

class DailyTotalsGrid extends StatelessWidget {
  final double deposits;
  final double withdrawals;
  final double floatTopUps;
  final double commission;
  final TimePeriod selectedPeriod;
  final DateTime? customDate;
  final Function(String)? onCategoryTap;
  final Function(TimePeriod)? onPeriodChanged;
  final Function(DateTime)? onCustomDateSelected;

  const DailyTotalsGrid({
    super.key,
    required this.deposits,
    required this.withdrawals,
    required this.floatTopUps,
    required this.commission,
    required this.selectedPeriod,
    this.customDate,
    this.onCategoryTap,
    this.onPeriodChanged,
    this.onCustomDateSelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(AppTheme.paddingMedium),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Period selector header
          _buildPeriodSelector(context, l10n, theme),
          const SizedBox(height: AppTheme.paddingMedium),

          // Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: AppTheme.paddingMedium,
            crossAxisSpacing: AppTheme.paddingMedium,
            childAspectRatio: 1.2,
            children: [
              _buildGridItem(
                context: context,
                icon: Icons.trending_up,
                iconColor: Colors.green,
                label: l10n.deposits,
                amount: deposits,
                category: 'deposits',
              ),
              _buildGridItem(
                context: context,
                icon: Icons.trending_down,
                iconColor: Colors.red,
                label: l10n.withdrawals,
                amount: withdrawals,
                category: 'withdrawals',
              ),
              _buildGridItem(
                context: context,
                icon: Icons.account_balance_wallet,
                iconColor: Colors.blue,
                label: l10n.floatTopUp,
                amount: floatTopUps,
                category: 'float',
              ),
              _buildGridItem(
                context: context,
                icon: Icons.monetization_on,
                iconColor: Colors.orange,
                label: l10n.commission,
                amount: commission,
                category: 'commission',
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPeriodSelector(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
      child: Row(
        children: [
          Text(
            l10n.selectPeriod,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(width: AppTheme.paddingMedium),
          Expanded(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: TimePeriod.values.map((period) {
                  final isSelected = period == selectedPeriod;
                  return Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: GestureDetector(
                      onTap: () => _handlePeriodTap(context, period),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.surface,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.outline.withValues(alpha: 0.3),
                          ),
                        ),
                        child: Text(
                          _getPeriodLabel(period, l10n),
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: isSelected
                                ? theme.colorScheme.onPrimary
                                : theme.colorScheme.onSurface,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handlePeriodTap(BuildContext context, TimePeriod period) {
    if (period == TimePeriod.custom) {
      _showDatePicker(context);
    } else {
      onPeriodChanged?.call(period);
    }
  }

  Future<void> _showDatePicker(BuildContext context) async {
    final l10n = context.l10n;
    final selectedDate = await showDatePicker(
      context: context,
      initialDate: customDate ?? DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      helpText: l10n.selectDate,
      cancelText: l10n.cancel,
      confirmText: l10n.ok,
    );

    if (selectedDate != null) {
      onCustomDateSelected?.call(selectedDate);
      onPeriodChanged?.call(TimePeriod.custom);
    }
  }

  String _getPeriodLabel(TimePeriod period, AppLocalizations l10n) {
    switch (period) {
      case TimePeriod.today:
        return l10n.today;
      case TimePeriod.week:
        return l10n.thisWeek;
      case TimePeriod.month:
        return l10n.thisMonth;
      case TimePeriod.custom:
        if (customDate != null) {
          return '${customDate!.day}/${customDate!.month}/${customDate!.year}';
        }
        return l10n.custom;
    }
  }

  Widget _buildGridItem({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String label,
    required double amount,
    required String category,
    bool showSign = false,
  }) {
    final theme = Theme.of(context);

    return GestureDetector(
      onTap: () => onCategoryTap?.call(category),
      child: Container(
        padding: const EdgeInsets.all(AppTheme.paddingMedium),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.shadow.withValues(alpha: 0.05),
              blurRadius: 4,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 24,
              ),
            ),
            const SizedBox(height: AppTheme.paddingSmall),

            // Label
            Text(
              label,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: AppTheme.paddingSmall),

            // Amount
            Text(
              showSign
                  ? MoneyFormatter.formatAmountWithSign(amount, isPositive: amount >= 0, showDecimals: false)
                  : MoneyFormatter.formatAmount(amount, showDecimals: false),
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: showSign
                    ? (amount >= 0 ? Colors.green : Colors.red)
                    : theme.colorScheme.onSurface,
                fontSize: 16,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }


}
