import 'package:flutter/material.dart';
import 'package:agent_kujukiza/domain/models/telco.dart';
import 'package:agent_kujukiza/domain/models/float_data.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/core/utils/money_formatter.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';

class TelcoFloatCard extends StatelessWidget {
  final FloatData floatData;
  final double threshold;
  final VoidCallback? onTap;

  const TelcoFloatCard({
    super.key,
    required this.floatData,
    this.threshold = 10000,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);
    final telco = floatData.telco;
    final isFloatOk = floatData.currentFloat >= threshold;
    final timeAgo = _getTimeAgo(floatData.lastUpdated, l10n);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
        padding: const EdgeInsets.all(AppTheme.paddingLarge),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              telco.brandColor.withValues(alpha: 0.1),
              telco.brandColor.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: telco.brandColor.withValues(alpha: 0.3),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: telco.brandColor.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with telco info
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: telco.brandColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    telco.icon,
                    style: const TextStyle(fontSize: 24),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${telco.displayName} ${l10n.currentFloat}',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '${l10n.lastUpdated} $timeAgo',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),
                // Float status indicator
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: isFloatOk ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isFloatOk ? l10n.floatOk : l10n.lowFloat,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Current float amount
            Text(
              MoneyFormatter.formatUGX(floatData.currentFloat, showDecimals: false),
              style: theme.textTheme.headlineLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: telco.brandColor.computeLuminance() > 0.5 
                    ? Colors.black87 
                    : telco.brandColor,
                fontSize: 32,
              ),
            ),

            const SizedBox(height: 12),

            // Net change indicator
            if (floatData.netChange != 0) ...[
              Row(
                children: [
                  Icon(
                    floatData.netChange > 0 ? Icons.trending_up : Icons.trending_down,
                    color: floatData.netChange > 0 ? Colors.green : Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${floatData.netChange > 0 ? '+' : ''}${MoneyFormatter.formatAmount(floatData.netChange, showDecimals: false)}',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: floatData.netChange > 0 ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'from starting float',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
            ],

            // MTN XtraFloat indicator (only for MTN)
            if (telco == Telco.mtn) ...[
              Row(
                children: [
                  Icon(
                    floatData.isXtraFloatActive ? Icons.check_circle : Icons.cancel,
                    color: floatData.isXtraFloatActive ? Colors.green : Colors.grey,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    floatData.isXtraFloatActive ? l10n.xtraFloatActive : l10n.xtraFloatInactive,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: floatData.isXtraFloatActive 
                          ? Colors.green 
                          : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  String _getTimeAgo(DateTime lastUpdated, AppLocalizations l10n) {
    final now = DateTime.now();
    final difference = now.difference(lastUpdated);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}
