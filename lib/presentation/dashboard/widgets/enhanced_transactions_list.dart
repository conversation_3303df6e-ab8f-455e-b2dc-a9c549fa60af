import 'package:flutter/material.dart';
import 'package:agent_kujukiza/domain/models/transaction_enhanced.dart';
import 'package:agent_kujukiza/domain/models/telco.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/core/utils/money_formatter.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';

class EnhancedTransactionsList extends StatelessWidget {
  final List<TransactionEnhanced> transactions;
  final bool isCompact;
  final VoidCallback? onViewAll;
  final Telco? filterTelco; // Filter by specific telco

  const EnhancedTransactionsList({
    super.key,
    required this.transactions,
    this.isCompact = false,
    this.onViewAll,
    this.filterTelco,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    // Filter transactions by telco if specified
    final filteredTransactions = filterTelco != null
        ? transactions.where((t) => t.telco == filterTelco).toList()
        : transactions;

    // Limit transactions if compact view
    final displayTransactions = isCompact && filteredTransactions.length > 3
        ? filteredTransactions.take(3).toList()
        : filteredTransactions;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppTheme.paddingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.all(AppTheme.paddingMedium),
            child: Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: theme.colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  filterTelco != null 
                      ? '${filterTelco!.displayName} ${l10n.recentTransactions}'
                      : l10n.recentTransactions,
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                if (isCompact && onViewAll != null)
                  TextButton(
                    onPressed: onViewAll,
                    child: Text(
                      l10n.viewAllTransactions,
                      style: TextStyle(
                        color: theme.colorScheme.primary,
                        fontSize: 12,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Transactions list
          if (displayTransactions.isEmpty)
            Padding(
              padding: const EdgeInsets.all(AppTheme.paddingLarge),
              child: Center(
                child: Column(
                  children: [
                    Icon(
                      Icons.receipt_outlined,
                      size: 48,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      l10n.noTransactionsYet,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: displayTransactions.length,
              separatorBuilder: (context, index) => Divider(
                height: 1,
                color: theme.colorScheme.outline.withValues(alpha: 0.1),
              ),
              itemBuilder: (context, index) {
                final transaction = displayTransactions[index];
                return _buildTransactionItem(context, transaction, theme);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildTransactionItem(
    BuildContext context, 
    TransactionEnhanced transaction, 
    ThemeData theme
  ) {
    final telco = transaction.telco;
    final amountColor = transaction.isPositive ? Colors.green : Colors.red;

    return ListTile(
      contentPadding: const EdgeInsets.symmetric(
        horizontal: AppTheme.paddingMedium,
        vertical: 8,
      ),
      leading: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Telco badge
          Container(
            padding: const EdgeInsets.all(4),
            decoration: BoxDecoration(
              color: telco.brandColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: telco.brandColor.withValues(alpha: 0.3),
              ),
            ),
            child: Text(
              telco.icon,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          const SizedBox(width: 8),
          // Transaction type icon
          _buildTransactionIcon(transaction.type, telco),
        ],
      ),
      title: Text(
        transaction.counterparty,
        style: theme.textTheme.bodyLarge?.copyWith(
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '${transaction.timeFormatted}, ${_formatDate(transaction.timestamp)}',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
          ),
          if (transaction.commission != null && transaction.commission! > 0)
            Text(
              'Commission: ${MoneyFormatter.formatAmount(transaction.commission!, showDecimals: false)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.orange,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
      trailing: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            transaction.displayAmount,
            style: theme.textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: amountColor,
            ),
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 2),
          if (transaction.newBalance != null)
            Text(
              'Bal: ${MoneyFormatter.formatAmount(transaction.newBalance!, showDecimals: false)}',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                fontSize: 10,
              ),
              overflow: TextOverflow.ellipsis,
            )
          else
            Text(
              'Bal: –––',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.3),
                fontSize: 10,
              ),
            ),
        ],
      ),
      onTap: () {
        _showTransactionDetails(context, transaction);
      },
    );
  }

  Widget _buildTransactionIcon(TransactionType type, Telco telco) {
    Color backgroundColor;
    Color iconColor;
    IconData iconData;

    switch (type) {
      case TransactionType.deposit:
        backgroundColor = Colors.green.withValues(alpha: 0.1);
        iconColor = Colors.green;
        iconData = Icons.arrow_upward;
        break;
      case TransactionType.withdrawal:
        backgroundColor = Colors.red.withValues(alpha: 0.1);
        iconColor = Colors.red;
        iconData = Icons.arrow_downward;
        break;
      case TransactionType.floatTopUp:
        backgroundColor = Colors.blue.withValues(alpha: 0.1);
        iconColor = Colors.blue;
        iconData = Icons.account_balance_wallet;
        break;
      case TransactionType.commission:
        backgroundColor = Colors.orange.withValues(alpha: 0.1);
        iconColor = Colors.orange;
        iconData = Icons.monetization_on;
        break;
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(
        iconData,
        color: iconColor,
        size: 16,
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final transactionDate = DateTime(date.year, date.month, date.day);

    if (transactionDate == today) {
      return 'Today';
    } else if (transactionDate == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  void _showTransactionDetails(BuildContext context, TransactionEnhanced transaction) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Text(transaction.telco.icon),
            const SizedBox(width: 8),
            Text('${transaction.telco.displayName} Transaction'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (transaction.rawSms != null) ...[
              const Text('📄 Full SMS:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 8),
              Text(transaction.rawSms!),
              const SizedBox(height: 16),
            ],
            const Text('↳ Transaction ID:', style: TextStyle(fontWeight: FontWeight.bold)),
            const SizedBox(height: 4),
            Text(transaction.id),
            if (transaction.commission != null) ...[
              const SizedBox(height: 8),
              const Text('💰 Commission:', style: TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Text(MoneyFormatter.formatUGX(transaction.commission!)),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
