import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/routing/app_router.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/float_summary_card.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/daily_accounting_card.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/language_selection_modal.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/daily_totals_grid.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/recent_transactions_list.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/bottom_navigation_bar.dart';
import 'package:agent_kujukiza/core/enums/time_period.dart';

class DashboardScreen extends StatefulWidget {
  const DashboardScreen({super.key});

  @override
  State<DashboardScreen> createState() => _DashboardScreenState();
}

class _DashboardScreenState extends State<DashboardScreen> {
  TimePeriod selectedPeriod = TimePeriod.today;
  DateTime? customDate;

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    // Mock data - replace with actual data from your state management
    final currentFloat = 456789.50;
    final startingFloat = 300000.0; // Amount they started the day with
    final dailyCommission = 12500.0; // Commission earned today
    final floatLastUpdated = DateTime.now().subtract(const Duration(minutes: 15));

    // Get period-specific data
    final periodData = _getPeriodData(selectedPeriod, customDate);
    final deposits = periodData['deposits']!;
    final withdrawals = periodData['withdrawals']!;
    final floatTopUps = periodData['floatTopUps']!;
    final commission = periodData['commission']!;

    // Mock recent transactions with realistic amounts
    final recentTransactions = [
      Transaction(
        id: 'ABC123XYZ',
        type: TransactionType.deposit,
        amount: 125000.0,
        newBalance: 456789.50,
        counterparty: 'From John Doe',
        timestamp: DateTime.now().subtract(const Duration(hours: 1)),
        rawSms: 'You have received UGX 125,000.00 from John Doe. New balance: UGX 456,789.50. Ref: ABC123XYZ',
      ),
      Transaction(
        id: 'DEF456ABC',
        type: TransactionType.withdrawal,
        amount: 75000.0,
        newBalance: 331789.50,
        counterparty: 'To Mary Smith',
        timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        rawSms: 'You have sent UGX 75,000.00 to Mary Smith. New balance: UGX 331,789.50. Ref: DEF456ABC',
      ),
      Transaction(
        id: 'GHI789DEF',
        type: TransactionType.floatTopUp,
        amount: 200000.0,
        newBalance: 406789.50,
        counterparty: 'Float Purchase',
        timestamp: DateTime.now().subtract(const Duration(hours: 5)),
        rawSms: 'Float purchase successful. UGX 200,000.00 added. New balance: UGX 406,789.50. Ref: GHI789DEF',
      ),
    ];

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          l10n.dashboard,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        centerTitle: true,
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          // Language switching button - more visible
          Container(
            margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: IconButton(
              onPressed: () {
                // Show language selection modal
                LanguageSelectionModal.show(context);
              },
              icon: Icon(
                Icons.language,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              tooltip: l10n.switchLanguage,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Top Section: Current Float Status
            FloatSummaryCard(
              currentFloat: currentFloat,
              lastUpdated: floatLastUpdated,
              threshold: 10000,
            ),

            // Daily Accounting Summary
            DailyAccountingCard(
              startingFloat: startingFloat,
              dailyCommission: dailyCommission,
              currentFloat: currentFloat,
            ),

            // Middle Section: Daily Totals Grid
            DailyTotalsGrid(
              deposits: deposits,
              withdrawals: withdrawals,
              floatTopUps: floatTopUps,
              commission: commission,
              selectedPeriod: selectedPeriod,
              customDate: customDate,
              onCategoryTap: (category) {
                // Navigate to transactions screen with filter
                context.go(AppRoutes.transactions);
              },
              onPeriodChanged: (period) {
                setState(() {
                  selectedPeriod = period;
                });
              },
              onCustomDateSelected: (date) {
                setState(() {
                  customDate = date;
                });
              },
            ),

            // Bottom Section: Recent Transactions (Compact)
            RecentTransactionsList(
              transactions: recentTransactions,
              isCompact: true,
              onViewAll: () {
                context.go(AppRoutes.transactions);
              },
            ),

            // Add bottom padding for navigation bar
            const SizedBox(height: 80),
          ],
        ),
      ),
      bottomNavigationBar: const AppBottomNavigationBar(
        currentIndex: 0, // Home tab is active
      ),
    );
  }

  /// Get mock data based on selected time period
  Map<String, double> _getPeriodData(TimePeriod period, DateTime? customDate) {
    switch (period) {
      case TimePeriod.today:
        return {
          'deposits': 125000.0,
          'withdrawals': 75000.0,
          'floatTopUps': 200000.0,
          'commission': 12500.0,
        };
      case TimePeriod.week:
        return {
          'deposits': 850000.0,
          'withdrawals': 520000.0,
          'floatTopUps': 400000.0,
          'commission': 85000.0,
        };
      case TimePeriod.month:
        return {
          'deposits': 3200000.0,
          'withdrawals': 2100000.0,
          'floatTopUps': 800000.0,
          'commission': 320000.0,
        };
      case TimePeriod.custom:
        if (customDate != null) {
          // Generate mock data based on selected date
          final dayOfYear = customDate.difference(DateTime(customDate.year, 1, 1)).inDays;
          final seed = dayOfYear % 10;
          return {
            'deposits': 50000.0 + (seed * 15000.0),
            'withdrawals': 30000.0 + (seed * 8000.0),
            'floatTopUps': 100000.0 + (seed * 20000.0),
            'commission': 5000.0 + (seed * 1500.0),
          };
        }
        return {
          'deposits': 0.0,
          'withdrawals': 0.0,
          'floatTopUps': 0.0,
          'commission': 0.0,
        };
    }
  }
}
