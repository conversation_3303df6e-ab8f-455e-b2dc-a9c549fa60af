import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/domain/models/telco.dart';
import 'package:agent_kujukiza/domain/models/float_data.dart';
import 'package:agent_kujukiza/domain/models/transaction_enhanced.dart';
import 'package:agent_kujukiza/data/services/float_service.dart';
import 'package:agent_kujukiza/routing/app_router.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/telco_float_card.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/telco_metrics_card.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/enhanced_transactions_list.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/language_selection_modal.dart';
import 'package:agent_kujukiza/presentation/dashboard/widgets/bottom_navigation_bar.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';

class DualTelcoDashboardScreen extends StatefulWidget {
  const DualTelcoDashboardScreen({super.key});

  @override
  State<DualTelcoDashboardScreen> createState() => _DualTelcoDashboardScreenState();
}

class _DualTelcoDashboardScreenState extends State<DualTelcoDashboardScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  Map<Telco, FloatData> _floatData = {};
  List<TransactionEnhanced> _transactions = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load float data
      final floatData = await FloatService.getAllFloatData();
      
      // Load transactions
      final transactions = await FloatService.getTransactions();

      // Generate mock data if no real data exists
      final mockData = _generateMockData();

      setState(() {
        _floatData = floatData.isNotEmpty ? floatData : mockData;
        _transactions = transactions.isNotEmpty ? transactions : _generateMockTransactions();
        _isLoading = false;
      });
    } catch (e) {
      // Fallback to mock data
      setState(() {
        _floatData = _generateMockData();
        _transactions = _generateMockTransactions();
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    if (_isLoading) {
      return Scaffold(
        body: const Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      appBar: AppBar(
        title: Text(
          l10n.dashboard,
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        centerTitle: true,
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        actions: [
          // Language switching button
          Container(
            margin: const EdgeInsets.only(right: 16, top: 8, bottom: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: IconButton(
              onPressed: () {
                LanguageSelectionModal.show(context);
              },
              icon: Icon(
                Icons.language,
                color: theme.colorScheme.primary,
                size: 24,
              ),
              tooltip: l10n.switchLanguage,
            ),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(Telco.mtn.icon),
                  const SizedBox(width: 8),
                  Text(Telco.mtn.displayName),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(Telco.airtel.icon),
                  const SizedBox(width: 8),
                  Text(Telco.airtel.displayName),
                ],
              ),
            ),
          ],
          labelColor: theme.colorScheme.primary,
          unselectedLabelColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          indicatorColor: theme.colorScheme.primary,
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTelcoView(Telco.mtn),
          _buildTelcoView(Telco.airtel),
        ],
      ),
      bottomNavigationBar: const AppBottomNavigationBar(
        currentIndex: 0, // Home tab is active
      ),
    );
  }

  Widget _buildTelcoView(Telco telco) {
    final floatData = _floatData[telco];
    
    if (floatData == null) {
      return const Center(
        child: Text('No data available for this telco'),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            const SizedBox(height: 16),

            // Float Summary Card
            TelcoFloatCard(
              floatData: floatData,
              threshold: 10000,
              onTap: () {
                // TODO: Navigate to float details
              },
            ),

            const SizedBox(height: 16),

            // Metrics Card
            TelcoMetricsCard(
              floatData: floatData,
              onTap: () {
                // TODO: Navigate to detailed metrics
              },
            ),

            const SizedBox(height: 16),

            // Recent Transactions
            EnhancedTransactionsList(
              transactions: _transactions,
              isCompact: true,
              filterTelco: telco,
              onViewAll: () {
                context.go(AppRoutes.transactions);
              },
            ),

            // Add bottom padding for navigation bar
            const SizedBox(height: 80),
          ],
        ),
      ),
    );
  }

  // Mock data generation for testing
  Map<Telco, FloatData> _generateMockData() {
    final now = DateTime.now();
    
    return {
      Telco.mtn: FloatData(
        telco: Telco.mtn,
        currentFloat: 456789.50,
        startingFloat: 300000.0,
        dailyDeposits: 125000.0,
        dailyWithdrawals: 75000.0,
        dailyFloatTopUps: 200000.0,
        dailyCommission: 8500.0,
        isXtraFloatActive: true,
        lastUpdated: now.subtract(const Duration(minutes: 15)),
      ),
      Telco.airtel: FloatData(
        telco: Telco.airtel,
        currentFloat: 234567.00,
        startingFloat: 200000.0,
        dailyDeposits: 85000.0,
        dailyWithdrawals: 45000.0,
        dailyFloatTopUps: 100000.0,
        dailyCommission: 4000.0,
        lastUpdated: now.subtract(const Duration(minutes: 30)),
      ),
    };
  }

  List<TransactionEnhanced> _generateMockTransactions() {
    final now = DateTime.now();
    
    return [
      TransactionEnhanced(
        id: 'MTN123XYZ',
        type: TransactionType.deposit,
        telco: Telco.mtn,
        amount: 125000.0,
        newBalance: 456789.50,
        counterparty: 'From John Doe',
        timestamp: now.subtract(const Duration(hours: 1)),
        rawSms: 'You have received UGX 125,000.00 from John Doe. New balance: UGX 456,789.50. Ref: MTN123XYZ',
        commission: 1250.0,
      ),
      TransactionEnhanced(
        id: 'AIR456ABC',
        type: TransactionType.withdrawal,
        telco: Telco.airtel,
        amount: 75000.0,
        newBalance: 234567.00,
        counterparty: 'To Mary Smith',
        timestamp: now.subtract(const Duration(hours: 2)),
        rawSms: 'You have sent UGX 75,000.00 to Mary Smith. New balance: UGX 234,567.00. Ref: AIR456ABC',
        commission: 750.0,
      ),
      TransactionEnhanced(
        id: 'MTN789DEF',
        type: TransactionType.floatTopUp,
        telco: Telco.mtn,
        amount: 200000.0,
        newBalance: 331789.50,
        counterparty: 'Float Purchase',
        timestamp: now.subtract(const Duration(hours: 3)),
        rawSms: 'Float purchase successful. UGX 200,000.00 added. New balance: UGX 331,789.50. Ref: MTN789DEF',
      ),
      TransactionEnhanced(
        id: 'AIR012GHI',
        type: TransactionType.deposit,
        telco: Telco.airtel,
        amount: 50000.0,
        newBalance: 309567.00,
        counterparty: 'From Peter Mukasa',
        timestamp: now.subtract(const Duration(hours: 4)),
        rawSms: 'You have received UGX 50,000.00 from Peter Mukasa. New balance: UGX 309,567.00. Ref: AIR012GHI',
        commission: 500.0,
      ),
    ];
  }
}
