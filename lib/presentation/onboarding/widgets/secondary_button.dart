import 'package:flutter/material.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';

class SecondaryButton extends StatelessWidget {
  final VoidCallback onPressed;
  final String text;
  final bool isLoading;
  final bool isDisabled;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final Color? borderColor;
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final double borderRadius;
  final Widget? leadingIcon;
  final Widget? trailingIcon;

  const SecondaryButton({
    super.key,
    required this.onPressed,
    required this.text,
    this.isLoading = false,
    this.isDisabled = false,
    this.width,
    this.height = 48.0,
    this.padding,
    this.borderColor,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.borderRadius = AppTheme.borderRadiusMedium,
    this.leadingIcon,
    this.trailingIcon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final buttonBorderColor = borderColor ?? theme.primaryColor;
    final disabledColor = buttonBorderColor.withValues(alpha: 0.5);
    final textStyle = theme.textTheme.labelLarge?.copyWith(
      color: isDisabled ? disabledColor : (textColor ?? theme.primaryColor),
      fontSize: fontSize,
      fontWeight: fontWeight ?? FontWeight.w600,
    );

    return SizedBox(
      width: width,
      height: height,
      child: OutlinedButton(
        onPressed: (isLoading || isDisabled) ? null : onPressed,
        style: OutlinedButton.styleFrom(
          foregroundColor: textColor ?? theme.primaryColor,
          side: BorderSide(
            color: isDisabled ? disabledColor : buttonBorderColor,
            width: 1.5,
          ),
          padding: padding,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
          elevation: 0,
          textStyle: textStyle,
        ),
        child: isLoading
            ? const SizedBox(
                width: 24,
                height: 24,
                child: CircularProgressIndicator(
                  color: Colors.blue,
                  strokeWidth: 2.5,
                ),
              )
            : Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  if (leadingIcon != null) ...[
                    leadingIcon!,
                    const SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: textStyle,
                    textAlign: TextAlign.center,
                  ),
                  if (trailingIcon != null) ...[
                    const SizedBox(width: 8),
                    trailingIcon!,
                  ],
                ],
              ),
      ),
    );
  }
}
