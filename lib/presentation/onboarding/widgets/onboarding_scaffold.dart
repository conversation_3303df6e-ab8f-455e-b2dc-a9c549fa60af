import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';

class OnboardingScaffold extends StatelessWidget {
  final Widget child;
  final bool showBackButton;
  final bool showSkipButton;
  final VoidCallback? onSkip;
  final VoidCallback? onBack;
  final int? currentStep;
  final int? totalSteps;

  const OnboardingScaffold({
    super.key,
    required this.child,
    this.showBackButton = true,
    this.showSkipButton = false,
    this.onSkip,
    this.onBack,
    this.currentStep,
    this.totalSteps,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Column(
          children: [
            // Modern top navigation
            _buildTopNavigation(context, l10n, theme),

            // Progress indicator (if steps are provided)
            if (currentStep != null && totalSteps != null)
              _buildProgressIndicator(theme),

            // Main content
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: AppTheme.paddingLarge),
                child: child,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopNavigation(BuildContext context, AppLocalizations l10n, ThemeData theme) {
    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.paddingMedium,
        vertical: AppTheme.paddingSmall,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Back button (only show if not the first screen)
          SizedBox(
            width: 48,
            height: 48,
            child: showBackButton
                ? IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios,
                      color: theme.colorScheme.onSurface,
                      size: 20,
                    ),
                    onPressed: () {
                      // Use custom back callback if provided, otherwise try to pop
                      if (onBack != null) {
                        onBack!();
                      } else if (context.canPop()) {
                        context.pop();
                      }
                    },
                    style: IconButton.styleFrom(
                      backgroundColor: theme.colorScheme.surface,
                      elevation: 0,
                      alignment: Alignment.center,
                      padding: const EdgeInsets.only(left: 2), // Slight offset to visually center the arrow
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                        side: BorderSide(
                          color: theme.colorScheme.outline.withValues(alpha: 0.2),
                        ),
                      ),
                    ),
                  )
                : const SizedBox.shrink(),
          ),

          // Skip button
          if (showSkipButton)
            TextButton(
              onPressed: onSkip ?? () {},
              style: TextButton.styleFrom(
                foregroundColor: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                padding: const EdgeInsets.symmetric(
                  horizontal: AppTheme.paddingMedium,
                  vertical: AppTheme.paddingSmall,
                ),
              ),
              child: Text(
                l10n.skip,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            )
          else
            const SizedBox(width: 48),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(ThemeData theme) {
    if (currentStep == null || totalSteps == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(
        horizontal: AppTheme.paddingLarge,
        vertical: AppTheme.paddingMedium,
      ),
      child: Row(
        children: List.generate(totalSteps!, (index) {
          final isActive = index < currentStep!;
          final isCurrent = index == currentStep! - 1;

          return Expanded(
            child: Container(
              height: 4,
              margin: EdgeInsets.only(
                right: index < totalSteps! - 1 ? 8 : 0,
              ),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(2),
                color: isActive || isCurrent
                    ? theme.colorScheme.primary
                    : theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          );
        }),
      ),
    );
  }
}
