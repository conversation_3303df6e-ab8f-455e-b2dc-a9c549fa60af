import 'package:flutter/foundation.dart';
import 'package:agent_kujukiza/domain/models/onboarding_state.dart';
import 'package:agent_kujukiza/data/services/permission_service.dart';
import 'package:agent_kujukiza/core/storage/local_prefs.dart';

class OnboardingController extends ChangeNotifier {
  OnboardingState _state = const OnboardingState();
  bool _isLoading = false;
  String? _errorMessage;

  OnboardingState get state => _state;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;

  bool get canProceedToNext => _state.canProceedToNext;
  bool get isOnboardingComplete => _state.isOnboardingComplete;
  OnboardingStep get currentStep => _state.currentStep;

  OnboardingController() {
    _initialize();
  }

  /// Initialize the controller and load saved state
  Future<void> _initialize() async {
    debugPrint('Initializing OnboardingController...');

    _setLoading(true);

    try {
      // Initialize services
      await LocalPrefs.init();
      await PermissionService.initialize();

      // Load saved onboarding state
      _state = await LocalPrefs.loadOnboardingState();

      // Refresh permission status to ensure accuracy
      await _refreshPermissionStatus();

      debugPrint('OnboardingController initialized with state: $_state');
    } catch (e) {
      debugPrint('Error initializing OnboardingController: $e');
      _setError('Failed to initialize onboarding');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh permission status and update state accordingly
  Future<void> _refreshPermissionStatus() async {
    try {
      final permissions = await PermissionService.checkAllPermissions();

      _state = _state.copyWith(
        hasGrantedSms: permissions['sms'] ?? false,
        hasGrantedBattery: permissions['battery'] ?? false,
        lastUpdated: DateTime.now(),
      );

      await _saveState();
    } catch (e) {
      debugPrint('Error refreshing permission status: $e');
    }
  }

  /// Set language selection (don't auto-proceed)
  Future<void> setLanguageSelected(String languageCode) async {
    debugPrint('Setting language selected: $languageCode');

    _setLoading(true);
    _clearError();

    try {
      // Save language preference
      await LocalPrefs.saveSelectedLanguage(languageCode);

      // Update state
      _state = _state.copyWith(
        hasSelectedLanguage: true,
        lastUpdated: DateTime.now(),
      );

      await _saveState();

      debugPrint('Language selection saved, ready to proceed');

    } catch (e) {
      debugPrint('Error setting language: $e');
      _setError('Failed to save language selection');
    } finally {
      _setLoading(false);
    }
  }

  /// Manually proceed to next step (called by UI)
  Future<void> proceedToNextStep() async {
    await _proceedToNextStep();
  }

  /// Request SMS permission
  Future<void> requestSmsPermission() async {
    debugPrint('Requesting SMS permission...');

    _setLoading(true);
    _clearError();

    try {
      final result = await PermissionService.requestSmsPermission();

      if (result.isGranted) {
        _state = _state.copyWith(
          hasGrantedSms: true,
          lastUpdated: DateTime.now(),
        );

        await _saveState();

        debugPrint('SMS permission granted, ready to proceed');

      } else {
        _setError(result.denialReason?.userFriendlyMessage ?? 'SMS permission denied');
      }

    } catch (e) {
      debugPrint('Error requesting SMS permission: $e');
      _setError('Failed to request SMS permission');
    } finally {
      _setLoading(false);
    }
  }

  /// Request battery optimization disable
  Future<void> requestBatteryOptimization() async {
    debugPrint('Requesting battery optimization disable...');

    _setLoading(true);
    _clearError();

    try {
      final result = await PermissionService.requestBatteryOptimizationDisable();

      if (result.isGranted) {
        _state = _state.copyWith(
          hasGrantedBattery: true,
          lastUpdated: DateTime.now(),
        );

        await _saveState();

        debugPrint('Battery optimization permission granted, ready to proceed');

      } else {
        _setError(result.denialReason?.userFriendlyMessage ?? 'Battery optimization permission denied');
      }

    } catch (e) {
      debugPrint('Error requesting battery optimization: $e');
      _setError('Failed to request battery optimization');
    } finally {
      _setLoading(false);
    }
  }

  /// Proceed to next onboarding step
  Future<void> _proceedToNextStep() async {
    final nextStep = _state.currentStep.nextStep;

    if (nextStep != null) {
      _state = _state.copyWith(
        currentStep: nextStep,
        lastUpdated: DateTime.now(),
      );

      // Only complete onboarding if ALL permissions are granted
      if (nextStep == OnboardingStep.completed && _state.allPermissionsGranted) {
        await _completeOnboarding();
      } else {
        await _saveState();
      }
    }
  }

  /// Go to previous onboarding step
  Future<void> goToPreviousStep() async {
    final previousStep = _state.currentStep.previousStep;

    if (previousStep != null) {
      _state = _state.copyWith(
        currentStep: previousStep,
        lastUpdated: DateTime.now(),
      );

      await _saveState();
    }
  }

  /// Skip current step (only for non-critical steps)
  Future<void> skipCurrentStep() async {
    // Only allow skipping non-permission steps
    if (!_state.currentStep.isPermissionStep) {
      await _proceedToNextStep();
    }
  }

  /// Complete the onboarding process (only if all permissions granted)
  Future<bool> completeOnboarding() async {
    debugPrint('Attempting to complete onboarding...');

    if (!_state.allPermissionsGranted) {
      debugPrint('Cannot complete onboarding - not all permissions granted');
      _setError('All permissions must be granted to complete setup');
      return false;
    }

    return await _completeOnboarding();
  }

  /// Internal method to complete onboarding
  Future<bool> _completeOnboarding() async {
    debugPrint('Completing onboarding...');

    try {
      _state = _state.copyWith(
        currentStep: OnboardingStep.completed,
        isOnboardingComplete: true,
        lastUpdated: DateTime.now(),
      );

      await _saveState();
      await LocalPrefs.markOnboardingComplete();

      debugPrint('Onboarding completed successfully');
      return true;

    } catch (e) {
      debugPrint('Error completing onboarding: $e');
      _setError('Failed to complete onboarding');
      return false;
    }
  }

  /// Force complete onboarding (for testing)
  Future<void> forceCompleteOnboarding() async {
    debugPrint('Force completing onboarding...');

    _state = _state.copyWith(
      currentStep: OnboardingStep.completed,
      hasSelectedLanguage: true,
      hasGrantedSms: true,
      hasGrantedBattery: true,
      isOnboardingComplete: true,
      lastUpdated: DateTime.now(),
    );

    await _saveState();
    await LocalPrefs.markOnboardingComplete();
  }

  /// Reset onboarding (for testing/debugging)
  Future<void> resetOnboarding() async {
    debugPrint('Resetting onboarding...');

    _setLoading(true);

    try {
      await LocalPrefs.resetOnboarding();

      _state = const OnboardingState();
      await _saveState();

      debugPrint('Onboarding reset successfully');

    } catch (e) {
      debugPrint('Error resetting onboarding: $e');
      _setError('Failed to reset onboarding');
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh current state (call when app resumes)
  Future<void> refreshState() async {
    debugPrint('Refreshing onboarding state...');

    try {
      await _refreshPermissionStatus();

      // Re-evaluate if onboarding should be complete
      if (_state.allStepsCompleted && !_state.isOnboardingComplete) {
        await _completeOnboarding();
      }

    } catch (e) {
      debugPrint('Error refreshing state: $e');
    }
  }

  /// Save current state to local storage
  Future<void> _saveState() async {
    try {
      await LocalPrefs.saveOnboardingState(_state);
      notifyListeners();
    } catch (e) {
      debugPrint('Error saving onboarding state: $e');
    }
  }

  /// Set loading state
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error message
  void _setError(String message) {
    _errorMessage = message;
    notifyListeners();
  }

  /// Clear error message
  void _clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  /// Open app settings for manual permission management
  Future<void> openAppSettings() async {
    await PermissionService.openAppSettings();
  }
}
