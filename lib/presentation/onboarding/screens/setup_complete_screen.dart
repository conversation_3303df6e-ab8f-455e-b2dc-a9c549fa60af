import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/presentation/onboarding/logic/onboarding_controller.dart';
import 'package:agent_kujukiza/domain/models/onboarding_state.dart';
import 'package:agent_kujukiza/presentation/onboarding/widgets/onboarding_scaffold.dart';
import 'package:agent_kujukiza/presentation/onboarding/widgets/primary_button.dart';
import 'package:agent_kujukiza/routing/app_router.dart';

class SetupCompleteScreen extends StatelessWidget {
  const SetupCompleteScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return OnboardingScaffold(
      showBackButton: false, // Setup complete - no going back
      currentStep: 4,
      totalSteps: 4,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Spacer(),
          Container(
            width: 150,
            height: 150,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check_circle,
              size: 100,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: AppTheme.paddingXLarge),
          Text(
            l10n.setupCompleteTitle,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: AppTheme.paddingLarge),
            child: Text(
              l10n.setupCompleteMessage,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          const Spacer(),
          Consumer<OnboardingController>(
            builder: (context, onboardingController, child) {
              final allPermissionsGranted = onboardingController.state.allPermissionsGranted;

              return Column(
                children: [
                  if (!allPermissionsGranted)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(AppTheme.paddingMedium),
                      margin: const EdgeInsets.only(bottom: AppTheme.paddingMedium),
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                        border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.warning,
                            color: Colors.orange,
                            size: 32,
                          ),
                          const SizedBox(height: AppTheme.paddingSmall),
                          Text(
                            'Setup Incomplete',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: AppTheme.paddingSmall),
                          Text(
                            'All permissions must be granted to complete setup and access the app.',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.orange.shade700,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          const SizedBox(height: AppTheme.paddingSmall),
                          Text(
                            'Missing: ${_getMissingPermissions(onboardingController.state)}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.orange.shade600,
                              fontWeight: FontWeight.w600,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  PrimaryButton(
                    onPressed: allPermissionsGranted
                        ? () async {
                            final success = await onboardingController.completeOnboarding();
                            if (success && context.mounted) {
                              context.go(AppRoutes.dashboard);
                            }
                          }
                        : () {
                            // Go back to grant missing permissions
                            if (!onboardingController.state.hasGrantedSms) {
                              context.go(AppRoutes.onboardingSmsPermission);
                            } else if (!onboardingController.state.hasGrantedBattery) {
                              context.go(AppRoutes.onboardingBatteryOptimization);
                            }
                          },
                    text: allPermissionsGranted ? l10n.goToDashboard : 'Grant Missing Permissions',
                    isLoading: onboardingController.isLoading,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: AppTheme.paddingXLarge),
        ],
      ),
    );
  }

  String _getMissingPermissions(OnboardingState state) {
    final missing = <String>[];
    if (!state.hasGrantedSms) missing.add('SMS Access');
    if (!state.hasGrantedBattery) missing.add('Battery Optimization');
    return missing.join(', ');
  }
}
