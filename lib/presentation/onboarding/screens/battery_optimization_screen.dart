import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/presentation/onboarding/logic/onboarding_controller.dart';
import 'package:agent_kujukiza/presentation/onboarding/widgets/onboarding_scaffold.dart';
import 'package:agent_kujukiza/presentation/onboarding/widgets/primary_button.dart';
import 'package:agent_kujukiza/presentation/onboarding/widgets/secondary_button.dart';
import 'package:agent_kujukiza/routing/app_router.dart';

class BatteryOptimizationScreen extends StatelessWidget {
  const BatteryOptimizationScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return OnboardingScaffold(
      showBackButton: true, // Can go back to SMS permission
      showSkipButton: true,
      currentStep: 3,
      totalSteps: 4,
      onBack: () => context.go(AppRoutes.onboardingSmsPermission),
      onSkip: () => context.go(AppRoutes.onboardingSetupComplete),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: AppTheme.paddingLarge),
          Icon(
            Icons.battery_saver_outlined,
            size: 80,
            color: Theme.of(context).colorScheme.primary,
          ),
          const SizedBox(height: AppTheme.paddingLarge),
          Text(
            l10n.batteryOptimizationTitle,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.paddingMedium),
          Text(
            l10n.batteryOptimizationMessage,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.paddingLarge),
          Container(
            padding: const EdgeInsets.all(AppTheme.paddingMedium),
            decoration: BoxDecoration(
              color: AppTheme.lightGray.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: AppTheme.iconSizeMedium,
                ),
                const SizedBox(width: AppTheme.paddingMedium),
                Expanded(
                  child: Text(
                    l10n.batteryOptimizationExplanation,
                    style: AppTheme.bodySmall,
                  ),
                ),
              ],
            ),
          ),
          const Spacer(),
          Consumer<OnboardingController>(
            builder: (context, onboardingController, child) {
              return Column(
                children: [
                  if (onboardingController.errorMessage != null)
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(AppTheme.paddingMedium),
                      margin: const EdgeInsets.only(bottom: AppTheme.paddingMedium),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
                        border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                      ),
                      child: Text(
                        onboardingController.errorMessage!,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.red,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  PrimaryButton(
                    onPressed: () async {
                      await onboardingController.requestBatteryOptimization();
                      // Only navigate if battery permission was granted
                      if (onboardingController.state.hasGrantedBattery && context.mounted) {
                        onboardingController.proceedToNextStep();
                        context.go(AppRoutes.onboardingSetupComplete);
                      }
                    },
                    text: l10n.enableBatteryOptimization,
                    isLoading: onboardingController.isLoading,
                  ),
                  const SizedBox(height: AppTheme.paddingMedium),
                  SecondaryButton(
                    onPressed: () {
                      // Don't allow skipping - this is a required permission
                      // Show message that this permission is required
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(l10n.batteryOptimizationExplanation),
                          backgroundColor: Colors.orange,
                        ),
                      );
                    },
                    text: l10n.skip,
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: AppTheme.paddingLarge),
        ],
      ),
    );
  }
}
