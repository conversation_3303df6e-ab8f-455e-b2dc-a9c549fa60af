import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import 'package:agent_kujukiza/core/themes/app_theme.dart';
import 'package:agent_kujukiza/domain/localization/app_localizations.dart';
import 'package:agent_kujukiza/domain/localization/locale_provider.dart';
import 'package:agent_kujukiza/presentation/onboarding/logic/onboarding_controller.dart';
import 'package:agent_kujukiza/presentation/onboarding/widgets/onboarding_scaffold.dart';
import 'package:agent_kujukiza/presentation/onboarding/widgets/primary_button.dart';
import 'package:agent_kujukiza/routing/app_router.dart';

class LanguageSelectionScreen extends StatelessWidget {
  const LanguageSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final l10n = context.l10n;

    return OnboardingScaffold(
      showBackButton: false, // First screen - nowhere to go back to
      currentStep: 1,
      totalSteps: 4,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const SizedBox(height: AppTheme.paddingLarge),
          // Modern title section
          Text(
            l10n.selectLanguage,
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.paddingSmall),
          Text(
            l10n.selectLanguageSubtitle,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppTheme.paddingLarge * 2),
          _buildLanguageOption(
            context,
            language: l10n.english,
            locale: const Locale('en'),
          ),
          const SizedBox(height: AppTheme.paddingLarge),
          _buildLanguageOption(
            context,
            language: l10n.luganda,
            locale: const Locale('lg'),
          ),
          const Spacer(),
          Consumer<OnboardingController>(
            builder: (context, onboardingController, child) {
              return PrimaryButton(
                onPressed: () {
                  // Proceed to next step and navigate
                  onboardingController.proceedToNextStep();
                  context.go(AppRoutes.onboardingSmsPermission);
                },
                text: l10n.continueText,
                isLoading: onboardingController.isLoading,
                isDisabled: !onboardingController.canProceedToNext,
              );
            },
          ),
          const SizedBox(height: AppTheme.paddingLarge),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(
    BuildContext context, {
    required String language,
    required Locale locale,
  }) {
    final localeProvider = Provider.of<LocaleProvider>(context, listen: false);
    final isSelected = localeProvider.locale.languageCode == locale.languageCode;

    return Card(
      elevation: isSelected ? 4 : 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        side: isSelected
            ? BorderSide(color: AppTheme.primaryColor, width: 2)
            : BorderSide.none,
      ),
      child: InkWell(
        onTap: () {
          final onboardingController = context.read<OnboardingController>();
          localeProvider.setLocale(locale);
          onboardingController.setLanguageSelected(locale.languageCode);
        },
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        child: Padding(
          padding: const EdgeInsets.all(AppTheme.paddingMedium),
          child: Row(
            children: [
              Icon(
                Icons.language,
                size: AppTheme.iconSizeMedium,
                color: isSelected ? AppTheme.primaryColor : AppTheme.darkGray,
              ),
              const SizedBox(width: AppTheme.paddingMedium),
              Text(
                language,
                style: AppTheme.bodyLarge.copyWith(
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  color: isSelected ? AppTheme.primaryColor : AppTheme.darkGray,
                ),
              ),
              const Spacer(),
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: AppTheme.primaryColor,
                  size: AppTheme.iconSizeMedium,
                ),
            ],
          ),
        ),
      ),
    );
  }
}
