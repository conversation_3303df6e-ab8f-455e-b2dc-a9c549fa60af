// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:agent_kujukiza/main.dart';
import 'package:agent_kujukiza/domain/localization/locale_provider.dart';

void main() {
  testWidgets('App loads language selection screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(
      ChangeNotifierProvider(
        create: (context) => LocaleProvider(),
        child: const AgentKujukizaApp(),
      ),
    );

    // Wait for the app to load
    await tester.pumpAndSettle();

    // Verify that the language selection screen loads
    expect(find.text('Select Language'), findsOneWidget);
  });
}
